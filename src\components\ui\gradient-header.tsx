import React from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';

interface GradientHeaderProps {
  backHref?: string;
  backText?: string;
  title?: string;
  className?: string;
}

const GradientHeader: React.FC<GradientHeaderProps> = ({
  backHref = '/',
  backText = 'Nazad',
  title = 'INFLUEXUS',
  className
}) => {
  return (
    <header className={cn(
      'relative z-10 border-b border-white/20 backdrop-blur-sm bg-white/10',
      className
    )}>
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        <Link
          href={backHref}
          className="flex items-center space-x-2 text-white/80 hover:text-white transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>{backText}</span>
        </Link>
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/30">
            <span className="text-white font-bold text-lg">
              🔗
            </span>
          </div>
          <span className="text-xl font-bold text-white">
            {title}
          </span>
        </div>
      </div>
    </header>
  );
};

export { GradientHeader };
