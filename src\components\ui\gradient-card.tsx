import React from 'react';
import { cn } from '@/lib/utils';
import { Card, CardProps } from '@/components/ui/card';

interface GradientCardProps extends CardProps {
  variant?: 'primary' | 'secondary' | 'story' | 'subtle' | 'warm' | 'sunset' | 'ocean' | 'forest' | 'cosmic';
  glassEffect?: boolean;
}

const GradientCard = React.forwardRef<HTMLDivElement, GradientCardProps>(
  ({ className, variant = 'primary', glassEffect = true, ...props }, ref) => {
    const gradientClasses = {
      primary: 'bg-instagram-primary',
      secondary: 'bg-instagram-secondary',
      story: 'bg-instagram-story',
      subtle: 'bg-instagram-subtle',
      warm: 'bg-instagram-warm',
      sunset: 'bg-instagram-sunset',
      ocean: 'bg-instagram-ocean',
      forest: 'bg-instagram-forest',
      cosmic: 'bg-instagram-cosmic'
    };

    return (
      <Card
        ref={ref}
        className={cn(
          gradientClasses[variant],
          glassEffect && 'glass-instagram border-white/20 shadow-2xl',
          className
        )}
        {...props}
      />
    );
  }
);

GradientCard.displayName = 'GradientCard';

export { GradientCard };
