-- Ažuriranje database view-ova da koriste public_display_name umje<PERSON> full_name
-- Pokrenuti u Supabase SQL Editor

-- 1. Prvo dodati public_display_name polje u profiles tabelu ako ne postoji
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS public_display_name VARCHAR(100);

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> helper funkcije za prikaz imena
CREATE OR REPLACE FUNCTION get_display_name(
  public_display_name_param VARCHAR,
  full_name_param VARCHAR,
  username_param VARCHAR
) RETURNS VARCHAR AS $$
BEGIN
  -- Koristi public_display_name ako postoji i nije prazan
  IF public_display_name_param IS NOT NULL AND TRIM(public_display_name_param) != '' THEN
    RETURN TRIM(public_display_name_param);
  END IF;
  
  -- Fallback na 'Ime i prezime skriveno'
  RETURN 'Ime i prezime skriveno';
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 3. Uklanjanje postojećeg materialized view-a
DROP MATERIALIZED VIEW IF EXISTS influencer_search_view CASCADE;

-- 4. K<PERSON>iranje novog materialized view-a sa public_display_name logikom
CREATE MATERIALIZED VIEW influencer_search_view AS
SELECT 
  i.id,
  p.username,
  get_display_name(p.public_display_name, p.full_name, p.username) as full_name,
  p.public_display_name,
  p.avatar_url,
  p.bio,
  p.location,
  i.gender,
  i.age,
  i.is_verified,
  p.average_rating,
  p.total_reviews,
  
  -- Kategorije kao array
  COALESCE(
    ARRAY_AGG(DISTINCT c.name) FILTER (WHERE c.name IS NOT NULL),
    '{}'::varchar[]
  ) as categories,
  
  -- Platforme kao JSON
  COALESCE(
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'platform_id', plat.id,
        'platform_name', plat.name,
        'platform_icon', plat.icon,
        'handle', ip.handle,
        'followers_count', ip.followers_count,
        'is_verified', ip.is_verified
      ) ORDER BY plat.name
    ) FILTER (WHERE plat.id IS NOT NULL AND ip.is_active = true),
    '[]'::json
  ) as platforms,
  
  -- Pricing kao JSON
  COALESCE(
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'platform_id', ipp.platform_id,
        'content_type_id', ipp.content_type_id,
        'price', ipp.price,
        'platform_name', plat2.name,
        'content_type_name', ct.name
      ) ORDER BY ipp.price
    ) FILTER (WHERE ipp.id IS NOT NULL AND ipp.is_available = true),
    '[]'::json
  ) as pricing,

  -- Min/Max cijene u EUR
  COALESCE(MIN(ipp.price) FILTER (WHERE ipp.is_available = true), 0) as min_price,
  COALESCE(MAX(ipp.price) FILTER (WHERE ipp.is_available = true), 0) as max_price,

  -- Ukupni broj pratilaca
  COALESCE(SUM(ip.followers_count) FILTER (WHERE ip.is_active = true), 0) as total_followers,

  -- Search vector za full-text pretragu - koristi public_display_name umjesto full_name
  setweight(to_tsvector('simple', COALESCE(get_display_name(p.public_display_name, p.full_name, p.username), '')), 'A') ||
  setweight(to_tsvector('simple', COALESCE(p.username, '')), 'A') ||
  setweight(to_tsvector('simple', COALESCE(p.bio, '')), 'B') ||
  setweight(to_tsvector('simple', COALESCE(p.location, '')), 'C') ||
  setweight(to_tsvector('simple', COALESCE(string_agg(DISTINCT c.name, ' '), '')), 'B') as search_vector,

  i.created_at,
  i.updated_at

FROM influencers i
JOIN profiles p ON i.id = p.id
LEFT JOIN influencer_categories ic ON i.id = ic.influencer_id
LEFT JOIN categories c ON ic.category_id = c.id
LEFT JOIN influencer_platforms ip ON i.id = ip.influencer_id
LEFT JOIN platforms plat ON ip.platform_id = plat.id
LEFT JOIN influencer_platform_pricing ipp ON i.id = ipp.influencer_id
LEFT JOIN platforms plat2 ON ipp.platform_id = plat2.id
LEFT JOIN content_types ct ON ipp.content_type_id = ct.id
WHERE p.user_type = 'influencer'
GROUP BY 
  i.id, p.username, p.public_display_name, p.full_name, p.avatar_url, p.bio, p.location, 
  i.gender, i.age, i.is_verified, p.average_rating, p.total_reviews, 
  i.created_at, i.updated_at;

-- 5. Kreiranje indeksa za optimizaciju
CREATE UNIQUE INDEX IF NOT EXISTS idx_influencer_search_view_id ON influencer_search_view(id);
CREATE INDEX IF NOT EXISTS idx_influencer_search_view_username ON influencer_search_view(username);
CREATE INDEX IF NOT EXISTS idx_influencer_search_view_min_price ON influencer_search_view(min_price);
CREATE INDEX IF NOT EXISTS idx_influencer_search_view_max_price ON influencer_search_view(max_price);
CREATE INDEX IF NOT EXISTS idx_influencer_search_view_followers ON influencer_search_view(total_followers);
CREATE INDEX IF NOT EXISTS idx_influencer_search_view_search_vector ON influencer_search_view USING GIN(search_vector);

-- 6. Ažuriranje funkcije za refresh materialized view
CREATE OR REPLACE FUNCTION refresh_influencer_search_view()
RETURNS void AS $$
BEGIN
  -- Pokušaj concurrent refresh, ako ne uspe koristi obični refresh
  BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY influencer_search_view;
  EXCEPTION WHEN OTHERS THEN
    -- Fallback na obični refresh ako concurrent ne radi
    REFRESH MATERIALIZED VIEW influencer_search_view;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Ažuriranje search funkcije da koristi novu logiku
CREATE OR REPLACE FUNCTION search_influencers_advanced(
  search_query TEXT DEFAULT '',
  category_ids INTEGER[] DEFAULT NULL,
  platform_ids INTEGER[] DEFAULT NULL,
  content_type_ids INTEGER[] DEFAULT NULL,
  min_followers INTEGER DEFAULT 0,
  max_followers INTEGER DEFAULT 10000000,
  min_price DECIMAL DEFAULT 0,
  max_price DECIMAL DEFAULT 10000,
  location_filter TEXT DEFAULT '',
  gender_filter TEXT DEFAULT '',
  age_min INTEGER DEFAULT 13,
  age_max INTEGER DEFAULT 100,
  sort_by TEXT DEFAULT 'relevance',
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  username VARCHAR,
  full_name VARCHAR,
  public_display_name VARCHAR,
  avatar_url TEXT,
  bio TEXT,
  location VARCHAR,
  gender VARCHAR,
  age INTEGER,
  is_verified BOOLEAN,
  categories VARCHAR[],
  platforms JSON,
  pricing JSON,
  min_price DECIMAL,
  max_price DECIMAL,
  total_followers BIGINT,
  relevance_score REAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    isv.id,
    isv.username,
    isv.full_name,
    isv.public_display_name,
    isv.avatar_url,
    isv.bio,
    isv.location,
    isv.gender,
    isv.age,
    isv.is_verified,
    isv.categories,
    isv.platforms,
    isv.pricing,
    isv.min_price,
    isv.max_price,
    isv.total_followers,
    CASE 
      WHEN search_query = '' THEN 1.0
      ELSE ts_rank(isv.search_vector, plainto_tsquery('simple', search_query))
    END as relevance_score
  FROM influencer_search_view isv
  WHERE 
    -- Text search
    (search_query = '' OR isv.search_vector @@ plainto_tsquery('simple', search_query))
    
    -- Category filter
    AND (category_ids IS NULL OR isv.categories && ARRAY(SELECT unnest(category_ids)::VARCHAR))
    
    -- Platform filter (check if any of user's platforms match)
    AND (platform_ids IS NULL OR EXISTS (
      SELECT 1 FROM json_array_elements(isv.platforms) as p
      WHERE (p->>'platform_id')::INTEGER = ANY(platform_ids)
    ))
    
    -- Content type filter (check if user has pricing for any of these content types)
    AND (content_type_ids IS NULL OR EXISTS (
      SELECT 1 FROM json_array_elements(isv.pricing) as pr
      WHERE (pr->>'content_type_id')::INTEGER = ANY(content_type_ids)
    ))
    
    -- Followers filter
    AND isv.total_followers >= min_followers
    AND isv.total_followers <= max_followers
    
    -- Price filter
    AND (isv.min_price = 0 OR (isv.min_price >= min_price AND isv.max_price <= max_price))
    
    -- Location filter
    AND (location_filter = '' OR isv.location ILIKE '%' || location_filter || '%')
    
    -- Gender filter
    AND (gender_filter = '' OR isv.gender = gender_filter)
    
    -- Age filter
    AND (isv.age IS NULL OR (isv.age >= age_min AND isv.age <= age_max))
    
  ORDER BY 
    CASE 
      WHEN sort_by = 'relevance' AND search_query != '' THEN ts_rank(isv.search_vector, plainto_tsquery('simple', search_query))
      WHEN sort_by = 'followers_desc' THEN isv.total_followers
      WHEN sort_by = 'price_asc' THEN isv.min_price
      WHEN sort_by = 'price_desc' THEN isv.max_price
      ELSE 1.0
    END DESC,
    isv.total_followers DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Inicijalno refresh materialized view
SELECT refresh_influencer_search_view();

-- 9. Grant permissions
GRANT SELECT ON influencer_search_view TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_display_name(VARCHAR, VARCHAR, VARCHAR) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION search_influencers_advanced TO anon, authenticated;
GRANT EXECUTE ON FUNCTION refresh_influencer_search_view() TO authenticated;

-- 10. Dodavanje komentara za dokumentaciju
COMMENT ON FUNCTION get_display_name(VARCHAR, VARCHAR, VARCHAR) IS 'Helper funkcija za prikaz imena - koristi public_display_name ili fallback na "Ime i prezime skriveno"';
COMMENT ON MATERIALIZED VIEW influencer_search_view IS 'Optimizovani view za pretragu influencera sa public_display_name logikom';
COMMENT ON COLUMN profiles.public_display_name IS 'Javno ime koje influencer želi da se prikazuje biznis korisnicima';
