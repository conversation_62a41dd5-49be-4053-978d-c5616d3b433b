import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Get display name for a user profile
 * Uses public_display_name if available, otherwise falls back to 'Ime i prezime skriveno'
 * @param profile - User profile object with public_display_name, full_name, and username
 * @returns Display name string
 */
export function getDisplayName(profile: {
  public_display_name?: string | null;
  full_name?: string | null;
  username?: string | null;
} | null | undefined): string {
  // Check if profile exists
  if (!profile) {
    return 'Ime i prezime skriveno';
  }

  // Use public_display_name if available and not empty
  if (profile.public_display_name && profile.public_display_name.trim()) {
    return profile.public_display_name.trim();
  }

  // Fallback to hidden name message
  return 'Ime i prezime skriveno';
}

/**
 * Get initials from a name for avatar fallback
 * @param name - Name string or null
 * @returns Initials string (max 2 characters)
 */
export function getInitials(name: string | null | undefined): string {
  if (!name || !name.trim()) return '?';

  // Special case for hidden name
  if (name.trim() === 'Ime i prezime skriveno') return 'IP';

  return name
    .trim()
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
}
