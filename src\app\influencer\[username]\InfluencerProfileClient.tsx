'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import Image from 'next/image';
import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { TabsContent } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  MapPin,
  Users,
  Star,
  Verified,
  MessageCircle,
  Calendar,
  Globe,
  Package,
  CheckCircle,
  ArrowLeft,
} from 'lucide-react';
import { DirectOfferForm } from '@/components/offers/DirectOfferForm';
import { PackageOrderModal } from '@/components/offers/PackageOrderModal';
import { ResponsiveNavigation } from '@/components/navigation/ResponsiveNavigation';
import { DesktopHeader } from '@/components/navigation/DesktopHeader';
import { useAuth } from '@/contexts/AuthContext';
import { getOrCreateProfile } from '@/lib/profiles';
import { useBackButton } from '@/hooks/useBackButton';
import { toast } from 'sonner';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import type { PublicInfluencerProfile } from '@/lib/marketplace';
import { Rating } from '@/components/ui/rating';
import { formatPrice } from '@/lib/currency';

interface InfluencerProfileClientProps {
  profile: PublicInfluencerProfile;
}

export function InfluencerProfileClient({
  profile,
}: InfluencerProfileClientProps) {
  const { user } = useAuth();
  const router = useRouter();
  const [showOfferForm, setShowOfferForm] = useState(false);
  const [showPackageOrderModal, setShowPackageOrderModal] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<any>(null);
  const [businessProfile, setBusinessProfile] = useState<any>(null);
  const { shouldShowBackButton } = useBackButton();

  useEffect(() => {
    if (user && user.user_metadata?.user_type === 'business') {
      loadBusinessProfile();
    }
  }, [user]);

  const loadBusinessProfile = async () => {
    if (!user) return;

    try {
      const { data, error } = await getOrCreateProfile(user.id);
      if (data && !error) {
        setBusinessProfile(data);
      }
    } catch (error) {
      console.error('Error loading business profile:', error);
    }
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const starSize = "w-4 h-4";

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star key={i} className={`${starSize} fill-amber-400 text-amber-400`} />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className={`relative ${starSize}`}>
            <Star className={`${starSize} text-gray-300 absolute`} />
            <Star className={`${starSize} fill-amber-400 text-amber-400 absolute overflow-hidden`} style={{ clipPath: 'inset(0 50% 0 0)' }} />
          </div>
        );
      } else {
        stars.push(
          <Star key={i} className={`${starSize} text-gray-300`} />
        );
      }
    }

    return stars;
  };

  const getInitials = (name: string | null) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const getGenderLabel = (gender: string) => {
    switch (gender) {
      case 'male':
        return 'Muški';
      case 'female':
        return 'Ženski';
      case 'other':
        return 'Ostalo';
      default:
        return '';
    }
  };

  const handleOfferSuccess = () => {
    setShowOfferForm(false);
    toast.success('Ponuda je uspješno poslana!');
  };

  const handlePackageOrderSuccess = () => {
    setShowPackageOrderModal(false);
    setSelectedPackage(null);
    toast.success('Narudžba je uspješno poslana!');
  };

  const handleSendOffer = () => {
    if (!user) {
      toast.error('Morate biti prijavljeni da biste poslali ponudu');
      return;
    }
    setShowOfferForm(true);
  };

  const handleOrderPackage = (pkg: any) => {
    if (!user) {
      toast.error('Morate biti prijavljeni da biste naručili paket');
      return;
    }
    setSelectedPackage(pkg);
    setShowPackageOrderModal(true);
  };

  return (
    <div className="min-h-screen bg-background pb-16 md:pb-0">
      {/* Desktop Header */}
      <div className="hidden md:block">
        <DesktopHeader userType="business" profile={businessProfile} />
      </div>

      {/* Mobile Navigation */}
      <ResponsiveNavigation
        userType="business"
        profile={businessProfile}
        showBackButton={shouldShowBackButton}
        onBackClick={() => router.back()}
      />

      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto">
          {/* Desktop: Back button */}
          <div className="hidden md:block pt-6 pb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Nazad
            </Button>
          </div>

          {/* Mobile: Full-width image, Desktop: Centered layout */}
          <div className="flex flex-col">
            {/* Profile Image - Full width on mobile, centered on desktop */}
            <div className="w-full lg:flex lg:justify-center lg:pt-6">
              <div className="relative w-full aspect-square lg:w-96 lg:h-96 lg:rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={profile.profile_avatar_url || profile.avatar_url || '/placeholder.svg'}
                  alt={profile.full_name || profile.username}
                  fill
                  quality={100}
                  className="object-cover"
                  sizes="(max-width: 1024px) 100vw, 384px"
                  priority
                />
              </div>
            </div>

            {/* Profile Info - Below image, centered on desktop */}
            <div className="px-4 py-6 lg:text-center lg:max-w-2xl lg:mx-auto">
              <div className="space-y-4">
                {/* Name and Username */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 lg:justify-center">
                    <h1 className="text-2xl lg:text-3xl font-bold">
                      {profile.full_name || profile.username}
                    </h1>
                    {profile.is_verified && (
                      <Badge
                        variant="secondary"
                        className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0 px-2 py-1 shadow-lg"
                      >
                        <CheckCircle className="w-3 h-3 mr-1 fill-current" />
                        <span className="text-xs">Verified</span>
                      </Badge>
                    )}
                  </div>
                  <p className="text-lg text-muted-foreground">
                    @{profile.username}
                  </p>
                </div>

                {/* Rating */}
                <div className="flex justify-start lg:justify-center">
                  <div className="flex items-center gap-1">
                    <div className="flex items-center gap-0.5">
                      {renderStars(profile.average_rating || 0)}
                    </div>
                    <span className="text-sm font-medium">
                      {(profile.average_rating || 0).toFixed(1)}
                    </span>
                    {profile.total_reviews > 0 && (
                      <span className="text-sm text-muted-foreground">
                        ({profile.total_reviews})
                      </span>
                    )}
                  </div>
                </div>

                {/* Lokacija i demografija */}
                <div className="flex flex-wrap gap-4 text-sm text-muted-foreground lg:justify-center">
                  {profile.location && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      {profile.location}
                    </div>
                  )}
                  {profile.age && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {profile.age} godina
                    </div>
                  )}
                  {profile.gender && (
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {getGenderLabel(profile.gender)}
                    </div>
                  )}
                </div>


              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-6">
            {/* Bio */}
            {profile.bio && (
              <Card>
                <CardHeader>
                  <CardTitle>O meni</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {profile.bio}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Kategorije */}
            {profile.categories && profile.categories.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Kategorije sadržaja kojeg kreiram</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {profile.categories.map((category, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="text-sm"
                      >
                        {category.name || category.category_name}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Platforme */}
            {profile.platforms && profile.platforms.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Društvene mreže</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {profile.platforms.map((platform, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <span className="text-2xl">
                            {platform.platform_icon}
                          </span>
                          <div>
                            <p className="font-medium">
                              {platform.platform_name}
                            </p>
                            {platform.handle && (
                              <p className="text-sm text-muted-foreground">
                                @{platform.handle}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">
                            {formatFollowers(platform.followers_count)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            pratilaca
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Pricing Packages */}
            {profile.pricing && profile.pricing.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Paketi usluga
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    // Group packages by platform
                    const packagesByPlatform = profile.pricing.reduce((acc, pkg) => {
                      const platformKey = pkg.platform_id.toString();
                      if (!acc[platformKey]) {
                        acc[platformKey] = {
                          platform_id: pkg.platform_id,
                          platform_name: pkg.platform_name,
                          platform_icon: profile.platforms.find(p => p.platform_id === pkg.platform_id)?.platform_icon || '📱',
                          packages: []
                        };
                      }
                      acc[platformKey].packages.push(pkg);
                      return acc;
                    }, {} as Record<string, {
                      platform_id: number;
                      platform_name: string;
                      platform_icon: string;
                      packages: typeof profile.pricing;
                    }>);

                    const platforms = Object.values(packagesByPlatform);

                    if (platforms.length === 0) return null;

                    return (
                      <TabsWithBadge
                        defaultValue="sve"
                        className="w-full"
                        tabs={[
                          { name: "Sve", value: "sve", icon: <span>📋</span> },
                          ...platforms.map((platform) => ({
                            name: platform.platform_name,
                            value: platform.platform_id.toString(),
                            icon: <span>{platform.platform_icon}</span>
                          }))
                        ]}
                      >

                        {/* All packages tab */}
                        <TabsContent value="sve">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">Svi paketi</CardTitle>
                              <p className="text-sm text-muted-foreground">
                                Pregled svih dostupnih paketa na svim platformama
                              </p>
                            </CardHeader>
                            <CardContent className="space-y-3">
                              {profile.pricing.map((pkg, index) => (
                                <div
                                  key={index}
                                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                                >
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <span className="text-lg">
                                        {profile.platforms.find(p => p.platform_id === pkg.platform_id)?.platform_icon || '📱'}
                                      </span>
                                      <span className="font-medium text-sm">
                                        {pkg.auto_generated_name || pkg.content_type_name}
                                      </span>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                      {pkg.platform_name}
                                    </p>
                                  </div>
                                  <div className="text-right flex items-center gap-3">
                                    <span className="font-bold text-lg text-primary">
                                      {formatPrice(pkg.price)}
                                    </span>
                                    <Button
                                      size="sm"
                                      onClick={() => handleOrderPackage(pkg)}
                                      className="bg-green-600 hover:bg-green-700"
                                    >
                                      Naruči
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </CardContent>
                          </Card>
                        </TabsContent>

                        {/* Individual platform tabs */}
                        {platforms.map((platform) => (
                          <TabsContent
                            key={platform.platform_id}
                            value={platform.platform_id.toString()}
                          >
                            <Card>
                              <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-lg">
                                  <span className="text-xl">{platform.platform_icon}</span>
                                  {platform.platform_name}
                                </CardTitle>
                                <p className="text-sm text-muted-foreground">
                                  Paketi dostupni na {platform.platform_name} platformi
                                </p>
                              </CardHeader>
                              <CardContent className="space-y-3">
                                {platform.packages.map((pkg, index) => (
                                  <div
                                    key={index}
                                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/30 transition-colors"
                                  >
                                    <div className="flex-1">
                                      <div className="flex items-center gap-2 mb-1">
                                        <span className="font-medium text-sm">
                                          {pkg.auto_generated_name || pkg.content_type_name}
                                        </span>
                                      </div>
                                      <p className="text-xs text-muted-foreground">
                                        {platform.platform_name}
                                      </p>
                                    </div>
                                    <div className="text-right flex items-center gap-3">
                                      <span className="font-bold text-lg text-primary">
                                        {formatPrice(pkg.price)}
                                      </span>
                                      <Button
                                        size="sm"
                                        onClick={() => handleOrderPackage(pkg)}
                                        className="bg-green-600 hover:bg-green-700"
                                      >
                                        Naruči
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                              </CardContent>
                            </Card>
                          </TabsContent>
                        ))}
                      </TabsWithBadge>
                    );
                  })()}

                  {/* Custom Offer CTA */}
                  <div className="mt-6 pt-4 border-t">
                    <div className="text-center space-y-3">
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        Niste pronašli odgovarajući paket? Napravite ovdje ponudu prema Vašim zahtjevima
                      </p>
                      <Button
                        className="w-full"
                        size="lg"
                        onClick={handleSendOffer}
                      >
                        <MessageCircle className="h-5 w-5 mr-2" />
                        Pošaljite ponudu
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Portfolio */}
            {profile.portfolio_urls && profile.portfolio_urls.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Portfolio
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {profile.portfolio_urls.map((url, index) => (
                      <a
                        key={index}
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-blue-600 hover:underline">
                            {url}
                          </span>
                        </div>
                      </a>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>


        </div>
      </div>

      {/* Package Order Modal */}
      {selectedPackage && (
        <PackageOrderModal
          isOpen={showPackageOrderModal}
          onClose={() => {
            setShowPackageOrderModal(false);
            setSelectedPackage(null);
          }}
          onSuccess={handlePackageOrderSuccess}
          packageData={selectedPackage}
          influencerId={profile.id}
          influencerName={profile.full_name || profile.username}
        />
      )}

      {/* Direct Offer Modal */}
      <Dialog open={showOfferForm} onOpenChange={setShowOfferForm}>
        <DialogContent className="max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Pošaljite ponudu</DialogTitle>
          </DialogHeader>
          <DirectOfferForm
            influencerId={profile.id}
            influencerName={profile.full_name || profile.username}
            onSuccess={handleOfferSuccess}
            onCancel={() => setShowOfferForm(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
