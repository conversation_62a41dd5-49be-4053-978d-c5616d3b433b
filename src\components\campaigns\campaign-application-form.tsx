'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Loader2,
  Send,
  DollarSign,
  Calendar,
  FileText,
  Link as LinkIcon,
  User,
  Clock,
  MessageSquare,
  Users,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { createCampaignApplication } from '@/lib/campaigns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

// Schema za validaciju
const applicationSchema = z.object({
  proposedRate: z
    .number()
    .min(10, 'Minimalna cijena je 10 €')
    .max(50000, 'Maksimalna cijena je 50,000 €'),
  proposalText: z
    .string()
    .min(50, 'Prijedlog mora imati najmanje 50 karaktera')
    .max(2000, 'Prijedlog je predugačak'),
  deliveryTimeframe: z.string().min(1, 'Morate specificirati vremenski okvir'),
  experienceRelevant: z.string().optional(),
  audienceInsights: z.string().optional(),
  agreeToTerms: z
    .boolean()
    .refine(val => val === true, 'Morate se složiti sa uslovima'),
});

type ApplicationForm = z.infer<typeof applicationSchema>;

interface Campaign {
  id: string;
  title: string;
  budget: number;
  description: string;
  company_name: string;
  platforms: Array<{
    platform_name: string;
    platform_icon: string;
    posts_required: number;
    budget_per_post: number;
  }>;
}

interface CampaignApplicationFormProps {
  campaign: Campaign;
  onSuccess: () => void;
  onCancel: () => void;
}

export function CampaignApplicationForm({
  campaign,
  onSuccess,
  onCancel,
}: CampaignApplicationFormProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    setError,
  } = useForm<ApplicationForm>({
    resolver: zodResolver(applicationSchema),
    defaultValues: {
      proposedRate: undefined,
      deliveryTimeframe: '7-14 dana',
      agreeToTerms: false,
    },
  });

  const watchedValues = watch();

  const onSubmit = async (data: ApplicationForm) => {
    if (!user) return;

    setIsLoading(true);

    try {
      const applicationData = {
        campaign_id: campaign.id,
        influencer_id: user.id,
        proposed_rate: data.proposedRate,
        proposal_text: data.proposalText,
        delivery_timeframe: data.deliveryTimeframe,
        portfolio_links: null,
        experience_relevant: data.experienceRelevant || null,
        audience_insights: data.audienceInsights || null,
        status: 'pending' as const,
      };

      const { error } = await createCampaignApplication(applicationData);

      if (error) {
        setError('root', { message: 'Greška pri slanju aplikacije' });
        return;
      }

      onSuccess();
    } catch (error) {
      console.error('Error submitting application:', error);
      setError('root', { message: 'Neočekivana greška' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Campaign Summary */}
      <Card>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
            <div>
              <Label className="text-sm text-muted-foreground">Biznis</Label>
              <p className="font-medium">{campaign.company_name}</p>
            </div>
            <div>
              <Label className="text-sm text-muted-foreground">Budžet</Label>
              <p className="font-medium flex items-center gap-1">
                {campaign.budget.toLocaleString()} €
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Application Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Accordion type="multiple" defaultValue={["basic-info", "proposal", "terms"]} className="w-full">
          <AccordionItem value="basic-info">
            <AccordionTrigger className="text-lg font-semibold">
              <div className="flex items-center gap-2">
                Osnovna ponuda
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-4">
            {/* Proposed Rate */}
            <div>
              <Label htmlFor="proposedRate">Upišite Vašu ponudu (€) *</Label>
              <Input
                id="proposedRate"
                type="number"
                {...register('proposedRate', { valueAsNumber: true })}
              />
              {errors.proposedRate && (
                <p className="text-sm text-destructive mt-1">
                  {errors.proposedRate.message}
                </p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Budžet kampanje: {campaign.budget.toLocaleString()} €
              </p>
            </div>



            {/* Delivery Timeframe */}
            <div>
              <Label htmlFor="deliveryTimeframe">
                Vremenski okvir izvršavanja *
              </Label>
              <Select
                onValueChange={value => setValue('deliveryTimeframe', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Izaberite vremenski okvir" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-3 dana">1-3 dana</SelectItem>
                  <SelectItem value="3-7 dana">3-7 dana</SelectItem>
                  <SelectItem value="7-14 dana">7-14 dana</SelectItem>
                  <SelectItem value="14-30 dana">14-30 dana</SelectItem>
                  <SelectItem value="30+ dana">30+ dana</SelectItem>
                  <SelectItem value="custom">
                    Prilagođeno (objasniti u prijedlogu)
                  </SelectItem>
                </SelectContent>
              </Select>
              {errors.deliveryTimeframe && (
                <p className="text-sm text-destructive mt-1">
                  {errors.deliveryTimeframe.message}
                </p>
              )}
            </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="proposal">
            <AccordionTrigger className="text-lg font-semibold">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Vaš prijedlog
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-4">
            {/* Proposal Text */}
            <div>
              <Label htmlFor="proposalText">Vaš prijedlog *</Label>
              <Textarea
                id="proposalText"
                {...register('proposalText')}
                placeholder="Objasnite zašto ste savršen izbor za ovu kampanju, kako planirate pristupiti projektu, i šta možete ponuditi..."
                rows={6}
              />
              {errors.proposalText && (
                <p className="text-sm text-destructive mt-1">
                  {errors.proposalText.message}
                </p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                {watchedValues.proposalText?.length || 0}/2000 karaktera
              </p>
            </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="additional">
            <AccordionTrigger className="text-lg font-semibold">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Dodatne informacije
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-4">

            {/* Relevant Experience */}
            <div>
              <Label htmlFor="experienceRelevant">Relevantno iskustvo</Label>
              <Textarea
                id="experienceRelevant"
                {...register('experienceRelevant')}
                placeholder="Opišite vaše prethodno iskustvo sa sličnim kampanjama ili brendovima..."
                rows={3}
              />
            </div>

            {/* Audience Insights */}
            <div>
              <Label htmlFor="audienceInsights">Informacije o publici</Label>
              <Textarea
                id="audienceInsights"
                {...register('audienceInsights')}
                placeholder="Opišite vašu publiku - demografija, interesovanja, engagement rate..."
                rows={3}
              />
            </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="terms">
            <AccordionTrigger className="text-lg font-semibold">
              <div className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Uslovi korišćenja
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-4 pt-4">
            <div className="flex items-start space-x-2">
              <Checkbox
                id="agreeToTerms"
                checked={watchedValues.agreeToTerms}
                onCheckedChange={checked => setValue('agreeToTerms', !!checked)}
              />
              <div className="grid gap-1.5 leading-none">
                <Label
                  htmlFor="agreeToTerms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Slažem se sa uslovima korišćenja
                </Label>
                <p className="text-xs text-muted-foreground">
                  Slanjem aplikacije se slažete da će vaše informacije biti
                  podijeljene sa biznisom koji je kreirao kampanju.
                </p>
              </div>
            </div>
            {errors.agreeToTerms && (
              <p className="text-sm text-destructive mt-2">
                {errors.agreeToTerms.message}
              </p>
            )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        {/* Error message */}
        {errors.root && (
          <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-sm text-destructive">{errors.root.message}</p>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel}>
            Otkaži
          </Button>
          <div className="space-x-2">
            <Dialog open={showPreview} onOpenChange={setShowPreview}>
              <DialogTrigger asChild>
                <Button type="button" variant="outline">
                  Pregled
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Pregled aplikacije</DialogTitle>
                  <DialogDescription>
                    Pregledajte vašu aplikaciju prije slanja
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label className="font-medium">Kampanja:</Label>
                    <p className="text-sm">{campaign.title}</p>
                  </div>
                  <div>
                    <Label className="font-medium">Predložena cijena:</Label>
                    <p className="text-sm">
                      {watchedValues.proposedRate?.toLocaleString()} €
                    </p>
                  </div>
                  <div>
                    <Label className="font-medium">Vremenski okvir:</Label>
                    <p className="text-sm">{watchedValues.deliveryTimeframe}</p>
                  </div>
                  <div>
                    <Label className="font-medium">Prijedlog:</Label>
                    <p className="text-sm whitespace-pre-wrap">
                      {watchedValues.proposalText}
                    </p>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              <Send className="mr-2 h-4 w-4" />
              Pošalji aplikaciju
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
