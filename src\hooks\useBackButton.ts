'use client';

import { usePathname } from 'next/navigation';

/**
 * Hook koji detektuje da li treba prikazati "nazad" button u mobile navbar-u
 * na osnovu trenutne rute
 */
export function useBackButton() {
  const pathname = usePathname();

  // Definiši rute koje trebaju "nazad" button
  const backButtonRoutes = [
    // Pojedinačne kampanje
    /^\/campaigns\/[^\/]+$/,
    
    // Pojedinačne aplikacije (business view)
    /^\/dashboard\/biznis\/applications\/[^\/]+$/,
    
    // Pojedinačne ponude (business view)
    /^\/dashboard\/biznis\/offers\/[^\/]+$/,
    
    // Influencer profili
    /^\/influencer\/[^\/]+$/,
    
    // Pojedinačne aplikacije (influencer view)
    /^\/dashboard\/influencer\/applications\/[^\/]+$/,
    
    // Pojedinačne ponude (influencer view)
    /^\/dashboard\/influencer\/offers\/[^\/]+$/,
  ];

  // Provjeri da li trenutna ruta odgovara bilo kojoj od definisanih ruta
  const shouldShowBackButton = backButtonRoutes.some(route => route.test(pathname));

  return {
    shouldShowBackButton,
    pathname
  };
}
