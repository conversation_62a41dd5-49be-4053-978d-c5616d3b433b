'use client';

import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { TabsContent } from '@/components/ui/tabs';
import {
  Send,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  Euro,
  MessageCircle,
} from 'lucide-react';
import { ChatEnableButton } from '@/components/chat/ChatEnableButton';
import { getBusinessOffers, type DirectOfferWithDetails } from '@/lib/offers';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import { formatDate } from '@/lib/date-utils';
import { getDisplayName, getInitials } from '@/lib/utils';
import Link from 'next/link';

export default function BusinessOffersPage() {
  const [offers, setOffers] = useState<DirectOfferWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadOffers();
  }, []);

  const loadOffers = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await getBusinessOffers();
      if (error) {
        console.error('Error loading offers:', error);
      } else {
        setOffers(data || []);
      }
    } catch (error) {
      console.error('Error loading offers:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const pendingOffers = offers.filter(o => o.status === 'pending');
  const acceptedOffers = offers.filter(o => o.status === 'accepted');
  const rejectedOffers = offers.filter(o => o.status === 'rejected');
  const otherOffers = offers.filter(o => 
    !['pending', 'accepted', 'rejected'].includes(o.status)
  );

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Moje ponude</h1>
          <p className="text-muted-foreground mt-1">
            Upravljajte vašim direktnim ponudama influencerima
          </p>
        </div>



        {/* Offers Tabs */}
        <TabsWithBadge
          defaultValue="pending"
          className="space-y-6"
          tabs={[
            { name: "Na čekanju", value: "pending", count: pendingOffers.length },
            { name: "Prihvaćeno", value: "accepted", count: acceptedOffers.length },
            { name: "Odbijeno", value: "rejected", count: rejectedOffers.length },
            { name: "Ostalo", value: "other", count: otherOffers.length }
          ]}
        >
          <TabsContent value="pending" className="space-y-4">
            {pendingOffers.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Send className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Nema ponuda na čekanju</h3>
                  <p className="text-muted-foreground mb-4">
                    Nemate ponuda koje čekaju odgovor.
                  </p>
                  <Button asChild>
                    <Link href="/marketplace/influencers">
                      Pronađi influencere
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              pendingOffers.map(offer => (
                <OfferCard key={offer.id} offer={offer} />
              ))
            )}
          </TabsContent>

          <TabsContent value="accepted" className="space-y-4">
            {acceptedOffers.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Nema prihvaćenih ponuda</h3>
                  <p className="text-muted-foreground">
                    Nemate prihvaćenih ponuda.
                  </p>
                </CardContent>
              </Card>
            ) : (
              acceptedOffers.map(offer => (
                <OfferCard key={offer.id} offer={offer} />
              ))
            )}
          </TabsContent>

          <TabsContent value="rejected" className="space-y-4">
            {rejectedOffers.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <XCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Nema odbijenih ponuda</h3>
                  <p className="text-muted-foreground">
                    Nemate odbijenih ponuda.
                  </p>
                </CardContent>
              </Card>
            ) : (
              rejectedOffers.map(offer => (
                <OfferCard key={offer.id} offer={offer} />
              ))
            )}
          </TabsContent>

          <TabsContent value="other" className="space-y-4">
            {otherOffers.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <p className="text-muted-foreground">
                    Nemate drugih ponuda.
                  </p>
                </CardContent>
              </Card>
            ) : (
              otherOffers.map(offer => (
                <OfferCard key={offer.id} offer={offer} />
              ))
            )}
          </TabsContent>
        </TabsWithBadge>
      </div>
    </DashboardLayout>
  );
}

interface OfferCardProps {
  offer: DirectOfferWithDetails;
}

function OfferCard({ offer }: OfferCardProps) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge
            variant="outline"
            className="text-yellow-600 border-yellow-600"
          >
            <Clock className="w-3 h-3 mr-1" />
            Na čekanju
          </Badge>
        );
      case 'accepted':
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Prihvaćeno
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <XCircle className="w-3 h-3 mr-1" />
            Odbijeno
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Završeno
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="text-gray-600 border-gray-600">
            <XCircle className="w-3 h-3 mr-1" />
            Otkazano
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };



  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Influencer info */}
          <div className="flex items-center gap-3 md:w-64">
            <Avatar className="h-12 w-12">
              <AvatarImage
                src={offer.influencer.avatar_url || ''}
                alt={getDisplayName(offer.influencer)}
              />
              <AvatarFallback>
                {getInitials(getDisplayName(offer.influencer))}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium">
                {getDisplayName(offer.influencer)}
              </p>
              <p className="text-sm text-muted-foreground">
                @{offer.influencer.username}
              </p>
            </div>
          </div>

          {/* Offer details */}
          <div className="flex-1">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-semibold text-lg">{offer.title}</h3>
              {getStatusBadge(offer.status)}
            </div>
            <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
              {offer.description}
            </p>
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                {offer.budget.toLocaleString()} €
              </div>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {formatDistanceToNow(new Date(offer.created_at), {
                  addSuffix: true,
                  locale: hr,
                })}
              </div>
              {offer.deadline && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  Rok: {formatDate(offer.deadline)}
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col gap-2 md:w-32">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/biznis/offers/${offer.id}`}>
                <Eye className="h-4 w-4 mr-1" />
                Detalji
              </Link>
            </Button>
            {offer.status === 'accepted' && (
              <ChatEnableButton
                businessId={offer.business_id}
                influencerId={offer.influencer_id}
                offerId={offer.id}
                userType="business"
                variant="outline"
                size="sm"
                className="w-full"
              />
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
