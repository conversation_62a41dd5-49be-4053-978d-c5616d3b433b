'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getCampaignWithDetails, hasInfluencerApplied, updateCampaignStatus } from '@/lib/campaigns';
import { CampaignDetails, InfluencerApplicationResponse } from '@/lib/types';
import { getOrCreateProfile } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  ArrowLeft,
  Loader2,
  MapPin,
  Calendar,
  Euro,
  Users,
  Tag,
  MessageCircle,
  Clock,
  Target,
  CheckCircle,
  AlertCircle,
  Send,
  Edit,
  ChevronDown,
  ChevronUp,
  Hash,
  Ban,
  FileText,
  Package,
  Handshake,
  CreditCard,
  Shield,
  RotateCcw,
  Users2,
  Filter,
  Mail,
  Phone,
} from 'lucide-react';
import Link from 'next/link';
import { formatDistanceToNow } from 'date-fns';
import { bs } from 'date-fns/locale';
import { CampaignApplicationForm } from '@/components/campaigns/campaign-application-form';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';


export default function CampaignDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [campaign, setCampaign] = useState<CampaignDetails | null>(null);
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [isActivating, setIsActivating] = useState(false);
  const [applicationStatus, setApplicationStatus] =
    useState<InfluencerApplicationResponse>({ hasApplied: false });

  const campaignId = params.id as string;

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user && campaignId) {
      loadData();
    }
  }, [user, authLoading, campaignId, router]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load profile first
      const { data: profileData, error: profileError } = await getOrCreateProfile(user!.id);
      if (profileError || !profileData) {
        setError('Greška pri učitavanju profila');
        return;
      }
      setProfile(profileData);

      // Load campaign details
      const { data, error } = await getCampaignWithDetails(campaignId);

      if (error) {
        setError('Greška pri učitavanju kampanje');
        return;
      }

      if (!data) {
        setError('Kampanja nije pronađena');
        return;
      }

      setCampaign(data);

      // Check if current user is the owner of the campaign
      setIsOwner(data?.business_id === user?.id);

      // Check if influencer already applied (only for non-owners)
      if (data?.business_id !== user?.id && user?.id) {
        const { data: applicationData } = await hasInfluencerApplied(
          campaignId,
          user.id
        );
        if (applicationData) {
          setApplicationStatus(applicationData);
        }
      }
    } catch (err) {
      setError('Neočekivana greška');
    } finally {
      setLoading(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !campaign) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <CardTitle>Greška</CardTitle>
            <CardDescription>
              {error || 'Kampanja nije pronađena'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.back()}
            >
              Nazad
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      draft: { label: 'Nacrt', variant: 'secondary' as const },
      active: { label: 'Aktivna', variant: 'default' as const },
      paused: { label: 'Pauzirana', variant: 'outline' as const },
      completed: { label: 'Završena', variant: 'secondary' as const },
      cancelled: { label: 'Otkazana', variant: 'destructive' as const },
    };

    const statusInfo =
      statusMap[status as keyof typeof statusMap] || statusMap.draft;
    return <Badge variant={statusInfo.variant}>{statusInfo.label}</Badge>;
  };

  const getCollaborationTypeLabel = (type: string) => {
    const typeMap = {
      paid: 'Plaćena saradnja',
      barter: 'Barter (razmena)',
      hybrid: 'Hibridna saradnja',
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  const getPaymentTermsLabel = (terms: string) => {
    const termsMap = {
      upfront: 'Unapred',
      '50_50': '50% unapred, 50% po završetku',
      on_delivery: 'Po isporuci',
    };
    return termsMap[terms as keyof typeof termsMap] || terms;
  };

  const getUsageRightsLabel = (rights: string) => {
    const rightsMap = {
      single_use: 'Jednokratna upotreba',
      unlimited: 'Neograničena upotreba',
      '6_months': '6 meseci',
      '1_year': '1 godina',
    };
    return rightsMap[rights as keyof typeof rightsMap] || rights;
  };

  const getGenderLabel = (gender: string) => {
    const genderMap = {
      male: 'Muški',
      female: 'Ženski',
      other: 'Ostalo',
      prefer_not_to_say: 'Ne želim da kažem',
    };
    return genderMap[gender as keyof typeof genderMap] || 'Svi';
  };

  const handleActivateCampaign = async () => {
    if (!campaign) return;

    setIsActivating(true);
    try {
      const { data, error } = await updateCampaignStatus(campaign.id, 'active');

      if (error) {
        toast.error('Greška pri aktiviranju kampanje: ' + error.message);
        return;
      }

      // Update local state
      setCampaign(prev => prev ? { ...prev, status: 'active' } : null);
      toast.success('Kampanja je uspješno aktivirana!');
    } catch (error) {
      console.error('Error activating campaign:', error);
      toast.error('Greška pri aktiviranju kampanje');
    } finally {
      setIsActivating(false);
    }
  };

  // Use actual profile data for the header
  const headerProfile = profile || {
    avatar_url: user?.user_metadata?.avatar_url,
    full_name: user?.user_metadata?.full_name,
    username: user?.user_metadata?.username || user?.email?.split('@')[0],
  };

  return (
    <DashboardLayout requiredUserType={profile?.user_type || 'influencer'}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          {/* Desktop: Back button */}
          <div className="hidden md:block">
            <Button variant="outline" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Nazad
            </Button>
          </div>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold">{campaign.title}</h1>
            <div className="flex items-center gap-6 text-sm mt-1">
              <div className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4 text-blue-500" />
                <span className="font-medium text-foreground">
                  {campaign.applications_count || 0}
                </span>
                <span className="text-muted-foreground">
                  {(campaign.applications_count || 0) === 1 ? 'aplikacija' : 'aplikacija'}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-green-500" />
                <span className="text-muted-foreground">
                  Objavljena {campaign.created_at &&
                    formatDistanceToNow(new Date(campaign.created_at), {
                      addSuffix: true,
                      locale: bs,
                    })}
                </span>
              </div>
            </div>
          </div>
          {/* Status badge if needed */}
          {campaign.status && (
            <Badge variant={campaign.status === 'active' ? 'default' : 'secondary'}>
              {campaign.status === 'active' ? 'Aktivna' : 'Neaktivna'}
            </Badge>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">

            {/* Campaign Details */}
            <div className="space-y-6">
              {/* Description */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Opis kampanje
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                    {campaign.description}
                  </p>
                </CardContent>
              </Card>

              {/* Platforms and Content Types */}
              {campaign.platforms && campaign.platforms.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Platforme i tipovi sadržaja</CardTitle>
                    <CardDescription>
                      Gde influencer treba da objavi sadržaj i koji tip sadržaja
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {campaign.platforms.map((platform, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-3">
                            <span className="text-lg">{platform.icon}</span>
                            <span className="font-medium">{platform.name}</span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {platform.content_types?.map((type, typeIndex) => (
                              <Badge key={typeIndex} variant="secondary">
                                {type === 'post' ? 'Photo post' :
                                 type === 'video' ? 'Video' :
                                 type === 'story' ? 'Story' :
                                 type === 'reel' ? 'Shorts' : type}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}



              {/* Hashtags */}
              {campaign.hashtags && campaign.hashtags.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Hash className="h-5 w-5" />
                      Obavezni hashtag-ovi
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {campaign.hashtags.map((hashtag, index) => (
                        <Badge key={index} variant="outline" className="font-mono">
                          #{hashtag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Do Not Mention */}
              {campaign.do_not_mention && campaign.do_not_mention.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Ban className="h-5 w-5" />
                      Ne spominjati
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {campaign.do_not_mention.map((item, index) => (
                        <Badge key={index} variant="destructive">
                          {item}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Target Audience */}
              {(campaign.age_range_min || campaign.age_range_max || campaign.gender) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users2 className="h-5 w-5" />
                      Traženi profil influencera
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Age Range */}
                    {(campaign.age_range_min || campaign.age_range_max) && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Uzrast</span>
                        <span className="font-medium">
                          {campaign.age_range_min || 13} - {campaign.age_range_max || 100} godina
                        </span>
                      </div>
                    )}

                    {/* Gender */}
                    {campaign.gender && campaign.gender !== 'all' && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Pol</span>
                        <span className="font-medium">
                          {getGenderLabel(campaign.gender)}
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Content Categories */}
              {campaign.categories && campaign.categories.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Tag className="h-5 w-5" />
                      Kategorije sadržaja koji influencer inače kreira
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {campaign.categories.map((category: any) => (
                        <Badge key={category.id} variant="secondary" className="flex items-center gap-1">
                          {category.icon && (
                            <span className="text-sm">{category.icon}</span>
                          )}
                          {category.name}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Collaboration Type */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Handshake className="h-5 w-5" />
                    Tip saradnje
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="font-medium">
                    {getCollaborationTypeLabel(campaign.collaboration_type || 'paid')}
                  </p>
                </CardContent>
              </Card>



              {/* Additional Notes */}
              {campaign.additional_notes && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Dodatne napomene
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                      {campaign.additional_notes}
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>


          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Business Info */}
            <Card>
              <CardHeader>
                <CardTitle>Biznis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3 mb-4">
                  <Avatar>
                    <AvatarImage src={campaign.business_avatar || undefined} />
                    <AvatarFallback>
                      {campaign.company_name?.charAt(0).toUpperCase() || 'B'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">{campaign.company_name}</h3>
                    <p className="text-sm text-muted-foreground">
                      @{campaign.business_username}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Campaign Details */}
            <Card>
              <CardHeader>
                <CardTitle>Detalji kampanje</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Budžet</span>
                  <span className="font-medium">
                    {campaign.budget?.toLocaleString() || 'Nije specificirano'} €
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Tip saradnje
                  </span>
                  <span className="font-medium">
                    {getCollaborationTypeLabel(campaign.collaboration_type || 'paid')}
                  </span>
                </div>






              </CardContent>
            </Card>

            {/* Action Buttons - Different for Owner vs Influencer */}
            <div className="space-y-3">
              {isOwner ? (
                // Owner (Business) View
                <>
                  {campaign.status === 'draft' ? (
                    // Draft campaign - show Activate button
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={handleActivateCampaign}
                      disabled={isActivating}
                    >
                      {isActivating ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <CheckCircle className="mr-2 h-5 w-5" />
                      )}
                      {isActivating ? 'Aktiviranje...' : 'Aktiviraj kampanju'}
                    </Button>
                  ) : (
                    // Active campaign - show Applications button
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={() =>
                        router.push(
                          `/dashboard/biznis/applications?campaign=${campaign.id}`
                        )
                      }
                    >
                      <Users className="mr-2 h-5 w-5" />
                      Pregled aplikacija ({campaign.applications_count})
                    </Button>
                  )}

                  {/* Edit button only for draft campaigns */}
                  {campaign.status === 'draft' && (
                    <Button
                      variant="outline"
                      className="w-full"
                      size="lg"
                      onClick={() =>
                        router.push(`/campaigns/${campaign.id}/edit`)
                      }
                    >
                      <Edit className="mr-2 h-5 w-5" />
                      Uredi kampanju
                    </Button>
                  )}
                </>
              ) : (
                // Influencer View
                <>
                  {applicationStatus.hasApplied ? (
                    // Show application status
                    <Card className="w-full">
                      <CardContent className="pt-6">
                        <div className="flex items-center gap-3 mb-4">
                          <CheckCircle className="h-6 w-6 text-green-500" />
                          <div>
                            <h3 className="font-semibold">
                              Aplikacija poslana
                            </h3>
                            <p className="text-sm text-muted-foreground">
                              Prijavili ste se na ovu kampanju{' '}
                              {applicationStatus.appliedAt &&
                                formatDistanceToNow(
                                  new Date(applicationStatus.appliedAt),
                                  { addSuffix: true, locale: bs }
                                )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              applicationStatus.application?.status === 'accepted'
                                ? 'default'
                                : applicationStatus.application?.status === 'rejected'
                                  ? 'destructive'
                                  : 'secondary'
                            }
                          >
                            {applicationStatus.application?.status === 'pending' &&
                              'Čeka se odgovor'}
                            {applicationStatus.application?.status === 'accepted' &&
                              'Prihvaćeno'}
                            {applicationStatus.application?.status === 'rejected' &&
                              'Odbačeno'}
                          </Badge>
                        </div>
                        {applicationStatus.application?.status === 'pending' && (
                          <p className="text-sm text-muted-foreground mt-2">
                            Biznis će uskoro pregledati vašu aplikaciju i
                            odgovoriti.
                          </p>
                        )}
                        {applicationStatus.application?.id && (
                          <div className="mt-4">
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full"
                              onClick={() =>
                                router.push(
                                  `/dashboard/influencer/applications/${applicationStatus.application?.id}`
                                )
                              }
                            >
                              Pogledajte detalje vaše aplikacije
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ) : (
                    // Show application form
                    <Dialog
                      open={showApplicationForm}
                      onOpenChange={setShowApplicationForm}
                    >
                      <DialogTrigger asChild>
                        <Button className="w-full" size="lg">
                          <Send className="mr-2 h-5 w-5" />
                          Apliciraj
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>Aplikacija za kampanju</DialogTitle>
                        </DialogHeader>
                        <CampaignApplicationForm
                          campaign={{
                            id: campaign.id,
                            title: campaign.title,
                            budget: campaign.budget,
                            description: campaign.description,
                            company_name: campaign.company_name,
                            platforms: campaign.platforms,
                          }}
                          onSuccess={() => {
                            setShowApplicationForm(false);
                            // Reload to show application status
                            loadCampaign();
                          }}
                          onCancel={() => setShowApplicationForm(false)}
                        />
                      </DialogContent>
                    </Dialog>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
