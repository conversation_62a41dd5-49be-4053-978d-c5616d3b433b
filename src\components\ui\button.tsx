import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-semibold transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-white/20 transform hover:-translate-y-0.5",
  {
    variants: {
      variant: {
        default:
          'btn-instagram-primary',
        destructive:
          'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:shadow-xl rounded-xl hover:from-red-600 hover:to-red-700',
        outline:
          'btn-instagram-glass border-white/30',
        secondary:
          'btn-instagram-white',
        ghost:
          'hover:bg-white/10 text-white rounded-xl',
        link: 'text-white underline-offset-4 hover:underline hover:text-white/80',
      },
      size: {
        default: 'h-10 px-6 py-2 rounded-xl has-[>svg]:px-5',
        sm: 'h-8 gap-1.5 px-4 has-[>svg]:px-3 rounded-lg text-xs',
        lg: 'h-12 px-8 has-[>svg]:px-6 rounded-xl text-base',
        icon: 'size-10 rounded-xl',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : 'button';

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
