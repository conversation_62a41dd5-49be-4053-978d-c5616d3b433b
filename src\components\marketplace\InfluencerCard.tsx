'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, Star, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import type { InfluencerSearchResult, SearchFilters } from '@/lib/marketplace';
import { formatPrice } from '@/lib/currency';
import { getDisplayName } from '@/lib/utils';

interface InfluencerCardProps {
  influencer: InfluencerSearchResult;
  filters?: SearchFilters;
  disableClick?: boolean;
}

export function InfluencerCard({ influencer, filters, disableClick = false }: InfluencerCardProps) {
  // formatPrice function is now imported from @/lib/currency

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    const starSize = "w-2.5 h-2.5 md:w-3 md:h-3";

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star key={i} className={`${starSize} fill-amber-400 text-amber-400`} />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className={`relative ${starSize}`}>
            <Star className={`${starSize} text-gray-300 absolute`} />
            <Star className={`${starSize} fill-amber-400 text-amber-400 absolute overflow-hidden`} style={{ clipPath: 'inset(0 50% 0 0)' }} />
          </div>
        );
      } else {
        stars.push(
          <Star key={i} className={`${starSize} text-gray-300`} />
        );
      }
    }

    return stars;
  };

  const formatFollowers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // Rezervisano mjesto - uvijek 3 platforme
  const displayPlatforms = [
    ...influencer.platforms.slice(0, 3),
    ...Array(Math.max(0, 3 - influencer.platforms.length)).fill(null)
  ];

  // Filtriranje cijena na osnovu trenutnih filtera
  const getFilteredPricing = () => {
    let filteredPricing = [...influencer.pricing];

    // Filtriraj po platformama ako su specificirane
    if (filters?.platforms && filters.platforms.length > 0) {
      filteredPricing = filteredPricing.filter(pkg =>
        filters.platforms!.includes(pkg.platform_id)
      );
    }

    // Filtriraj po content tipovima ako su specificirani
    if (filters?.contentTypes && filters.contentTypes.length > 0) {
      filteredPricing = filteredPricing.filter(pkg =>
        filters.contentTypes!.includes(pkg.content_type_id)
      );
    }

    // Filtriraj po cijeni ako je specificirana
    if (filters?.minPrice !== undefined) {
      filteredPricing = filteredPricing.filter(pkg => pkg.price >= filters.minPrice!);
    }
    if (filters?.maxPrice !== undefined) {
      filteredPricing = filteredPricing.filter(pkg => pkg.price <= filters.maxPrice!);
    }

    // Ako nema filtriranih rezultata, vrati originalne
    return filteredPricing.length > 0 ? filteredPricing : influencer.pricing;
  };

  // Rezervisano mjesto - uvijek 2 paketa (ali sada filtrirane)
  const filteredPricing = getFilteredPricing();
  const displayPricing = [
    ...filteredPricing.slice(0, 2),
    ...Array(Math.max(0, 2 - filteredPricing.length)).fill(null)
  ];

  // Glavna kategorija za prikaz
  const mainCategory = influencer.categories?.[0]?.name || 'Influencer';

  // Provjeri da li su cijene filtrirane
  const isFiltered = filteredPricing.length !== influencer.pricing.length;

  const cardContent = (
    <div className={`p-1 bg-gradient-to-br from-pink-500 via-purple-500 to-orange-400 rounded-xl group transition-all duration-200 ${!disableClick ? 'hover:shadow-lg cursor-pointer' : 'cursor-default'}`}>
      <Card className="w-full overflow-hidden bg-white h-full py-0">
        <CardContent className="p-0">
            {/* Image section */}
            <div className="relative w-full aspect-square overflow-hidden bg-gray-100">
              <Image
                src={influencer.card_avatar_url || influencer.avatar_url || "/placeholder.svg"}
                alt={`${getDisplayName(influencer) !== 'Ime i prezime skriveno' ? getDisplayName(influencer) : influencer.username} profile`}
                fill
                quality={100}
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />

              {/* Verified badge - top left */}
              {influencer.is_verified && (
                <div className="absolute top-2 left-2 md:top-2.5 md:left-2.5 z-20">
                  <Badge
                    variant="secondary"
                    className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0 px-1.5 py-0.5 md:px-1.5 md:py-0.5 shadow-lg"
                  >
                    <CheckCircle className="w-2.5 h-2.5 md:w-2.5 md:h-2.5 mr-0.5 fill-current" />
                    <span className="text-xs">Verified</span>
                  </Badge>
                </div>
              )}

              {/* Star rating - top right */}
              <div className="absolute top-2 right-2 md:top-2.5 md:right-2.5 z-20">
                <div className="bg-white/95 backdrop-blur-sm rounded-full px-2 py-1 md:px-2.5 md:py-1 flex items-center gap-1 md:gap-1 shadow-md border border-white/20">
                  <div className="flex items-center gap-0.5">
                    {renderStars(influencer.average_rating || 0)}
                  </div>
                  <span className="text-gray-900 text-xs md:text-xs font-medium">{(influencer.average_rating || 0).toFixed(1)}</span>
                </div>
              </div>

              {/* Username - bottom left */}
              <div className="absolute bottom-2 left-2 md:bottom-2.5 md:left-2.5 z-20">
                <div className="bg-black/70 backdrop-blur-sm rounded-md px-1.5 py-0.5 border-0" style={{ backgroundColor: 'rgba(0, 0, 0, 0.7)', boxShadow: 'none' }}>
                  <h3 className="text-white font-medium text-xs">@{influencer.username}</h3>
                </div>
              </div>

              {/* Total followers - bottom right */}
              <div className="absolute bottom-2 right-2 md:bottom-2.5 md:right-2.5 z-20">
                <div className="bg-black/70 backdrop-blur-sm rounded-md px-1.5 py-0.5 border-0 inline-flex items-center" style={{ backgroundColor: 'rgba(0, 0, 0, 0.7)', boxShadow: 'none' }}>
                  <span className="text-white font-medium text-xs">
                    {formatFollowers(influencer.total_followers)}
                  </span>
                </div>
              </div>
            </div>

            {/* Info section */}
            <div className="p-3 space-y-2">
              {/* Location */}
              {(influencer.city || influencer.country) && (
                <div className="text-left">
                  <p className="text-xs text-gray-500">
                    {influencer.city && influencer.country ? `${influencer.city}, ${influencer.country}` : influencer.city || influencer.country}
                  </p>
                </div>
              )}

              {/* Bio - rezervisano mjesto za 2 reda */}
              <div className="h-8 flex items-start">
                <p className="text-xs text-gray-600 line-clamp-2 leading-4">
                  {influencer.bio || ''}
                </p>
              </div>



              {/* Separator */}
              <hr className="border-gray-200" />

              {/* Packages */}
              <div className="space-y-1.5">
                <div className="flex items-center justify-between">
                  <h4 className="text-xs font-semibold text-gray-700">Paketi</h4>
                  {isFiltered && (
                    <Badge variant="secondary" className="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-700 border-blue-200">
                      Filtrirano
                    </Badge>
                  )}
                </div>
                {displayPricing.slice(0, 2).map((pkg, index) => (
                  pkg ? (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-xs text-gray-600 truncate max-w-[120px]">
                        {pkg.content_type_name}
                      </span>
                      <span className="text-xs font-medium text-gradient bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent">
                        {formatPrice(pkg.price)}
                      </span>
                    </div>
                  ) : (
                    <div key={index} className="flex justify-between items-center opacity-30">
                      <span className="text-xs text-gray-400">-</span>
                      <span className="text-xs text-gray-400">-</span>
                    </div>
                  )
                ))}
              </div>

              {/* View Packages Button */}
              <div className="pt-1.5">
                <Button className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 group h-9 rounded-xl text-sm">
                  <span className="font-medium">Pogledaj pakete</span>
                  <ExternalLink className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
  );

  return disableClick ? cardContent : (
    <Link href={`/influencer/${influencer.username}`}>
      {cardContent}
    </Link>
  );
}
