'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { ShoppingCart, X, Check } from 'lucide-react';
import { toast } from 'sonner';
import { createPackageOrder } from '@/lib/offers';

interface PackageOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  packageData: {
    id: number;
    platform_id: number;
    platform_name: string;
    platform_icon: string;
    content_type_id: number;
    content_type_name: string;
    price: number;
    currency: string;
    quantity?: number;
    video_duration?: string | null;
    auto_generated_name: string;
  };
  influencerId: string;
  influencerName: string;
}

export function PackageOrderModal({
  isOpen,
  onClose,
  onSuccess,
  packageData,
  influencerId,
  influencerName,
}: PackageOrderModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [requirements, setRequirements] = useState('');
  const [message, setMessage] = useState('');

  const handleConfirmOrder = async () => {
    try {
      setIsLoading(true);

      const orderData = {
        influencer_id: influencerId,
        package_id: packageData.id,
        package_name: packageData.auto_generated_name,
        price: packageData.price,
        platform_name: packageData.platform_name,
        content_type_name: packageData.content_type_name,
        requirements: requirements.trim() || undefined,
        message: message.trim() || undefined,
      };

      const { error } = await createPackageOrder(orderData);

      if (error) {
        toast.error('Greška pri kreiranju narudžbe');
        return;
      }

      toast.success('Narudžba je uspješno poslana!');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating package order:', error);
      toast.error('Neočekivana greška');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Potvrda narudžbe
          </DialogTitle>
          <DialogDescription>
            Molimo potvrdite detalje vaše narudžbe
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-2">
          {/* Order Summary */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Sažetak narudžbe</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Influencer */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Influencer:</span>
                <span className="font-medium text-sm">{influencerName}</span>
              </div>

              {/* Package */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-base">{packageData.platform_icon}</span>
                  <Badge variant="secondary" className="text-xs">{packageData.platform_name}</Badge>
                </div>

                <div>
                  <p className="font-medium text-sm">{packageData.auto_generated_name}</p>
                  <p className="text-xs text-muted-foreground">
                    {packageData.content_type_name}
                    {packageData.quantity && packageData.quantity > 1 && ` • ${packageData.quantity}x`}
                    {packageData.video_duration && ` • ${packageData.video_duration}`}
                  </p>
                </div>
              </div>

              <hr className="my-2" />

              <div className="flex justify-between items-center">
                <span className="font-medium">Ukupno:</span>
                <span className="text-lg font-bold text-primary">
                  {packageData.price} {packageData.currency}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Requirements and Message */}
          <div className="space-y-3">
            <div>
              <Label htmlFor="requirements" className="text-sm font-medium">
                Specifični zahtjevi (opcionalno)
              </Label>
              <Textarea
                id="requirements"
                placeholder="Opišite specifične zahtjeve za sadržaj..."
                value={requirements}
                onChange={(e) => setRequirements(e.target.value)}
                className="mt-1 text-sm"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="message" className="text-sm font-medium">
                Poruka (opcionalno)
              </Label>
              <Textarea
                id="message"
                placeholder="Dodatna poruka za influencera..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="mt-1 text-sm"
                rows={2}
              />
            </div>
          </div>

          {/* Info message */}
          <div className="bg-muted/50 p-2 rounded-md">
            <p className="text-xs text-muted-foreground">
              Narudžba će biti poslana influenceru na razmatranje.
            </p>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-3 pt-3">
          <Button variant="outline" onClick={onClose} disabled={isLoading} className="flex-1">
            <X className="mr-2 h-4 w-4" />
            Otkaži
          </Button>
          <Button onClick={handleConfirmOrder} disabled={isLoading} className="flex-1">
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Šalje se...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Potvrdi
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
