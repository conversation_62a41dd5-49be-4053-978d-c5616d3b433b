'use client';

import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { TabsContent } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestTabsPage() {
  return (
    <div className="container mx-auto p-8 space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">Test Tabs Design</h1>
        <p className="text-muted-foreground">Poređenje originalnog i našeg dizajna</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Naš TabsWithBadge dizajn</CardTitle>
        </CardHeader>
        <CardContent>
          <TabsWithBadge
            defaultValue="pnpm"
            tabs={[
              { name: "pnpm", value: "pnpm", count: 9 },
              { name: "npm", value: "npm" },
              { name: "yarn", value: "yarn", count: 3 },
              { name: "bun", value: "bun" }
            ]}
          >
            <TabsContent value="pnpm">
              <div className="h-10 flex items-center justify-between border gap-2 rounded-md pl-3 pr-1.5">
                <code className="text-[13px]">pnpm dlx shadcn@latest add tabs</code>
              </div>
            </TabsContent>
            <TabsContent value="npm">
              <div className="h-10 flex items-center justify-between border gap-2 rounded-md pl-3 pr-1.5">
                <code className="text-[13px]">npx shadcn@latest add tabs</code>
              </div>
            </TabsContent>
            <TabsContent value="yarn">
              <div className="h-10 flex items-center justify-between border gap-2 rounded-md pl-3 pr-1.5">
                <code className="text-[13px]">yarn add shadcn@latest tabs</code>
              </div>
            </TabsContent>
            <TabsContent value="bun">
              <div className="h-10 flex items-center justify-between border gap-2 rounded-md pl-3 pr-1.5">
                <code className="text-[13px]">bunx --bun shadcn@latest add tabs</code>
              </div>
            </TabsContent>
          </TabsWithBadge>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Aplikacije test (4 taba)</CardTitle>
        </CardHeader>
        <CardContent>
          <TabsWithBadge
            defaultValue="all"
            tabs={[
              { name: "Sve", value: "all", count: 15 },
              { name: "Na čekanju", value: "pending", count: 5 },
              { name: "Prihvaćeno", value: "accepted", count: 8 },
              { name: "Odbačeno", value: "rejected", count: 2 }
            ]}
          >
            <TabsContent value="all">
              <div className="p-4 border rounded-md">
                <p>Sve aplikacije (15)</p>
              </div>
            </TabsContent>
            <TabsContent value="pending">
              <div className="p-4 border rounded-md">
                <p>Na čekanju (5)</p>
              </div>
            </TabsContent>
            <TabsContent value="accepted">
              <div className="p-4 border rounded-md">
                <p>Prihvaćeno (8)</p>
              </div>
            </TabsContent>
            <TabsContent value="rejected">
              <div className="p-4 border rounded-md">
                <p>Odbačeno (2)</p>
              </div>
            </TabsContent>
          </TabsWithBadge>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test sa 6 tabova (scrollable)</CardTitle>
          <p className="text-sm text-muted-foreground">Na mobilnom se mogu skrolovati horizontalno</p>
        </CardHeader>
        <CardContent>
          <TabsWithBadge
            defaultValue="all"
            tabs={[
              { name: "Sve", value: "all", count: 25 },
              { name: "Na čekanju", value: "pending", count: 8 },
              { name: "Prihvaćeno", value: "accepted", count: 12 },
              { name: "Odbačeno", value: "rejected", count: 3 },
              { name: "Završeno", value: "completed", count: 15 },
              { name: "Arhivirano", value: "archived", count: 7 }
            ]}
          >
            <TabsContent value="all">
              <div className="p-4 border rounded-md">
                <p>Sve (25)</p>
              </div>
            </TabsContent>
            <TabsContent value="pending">
              <div className="p-4 border rounded-md">
                <p>Na čekanju (8)</p>
              </div>
            </TabsContent>
            <TabsContent value="accepted">
              <div className="p-4 border rounded-md">
                <p>Prihvaćeno (12)</p>
              </div>
            </TabsContent>
            <TabsContent value="rejected">
              <div className="p-4 border rounded-md">
                <p>Odbačeno (3)</p>
              </div>
            </TabsContent>
            <TabsContent value="completed">
              <div className="p-4 border rounded-md">
                <p>Završeno (15)</p>
              </div>
            </TabsContent>
            <TabsContent value="archived">
              <div className="p-4 border rounded-md">
                <p>Arhivirano (7)</p>
              </div>
            </TabsContent>
          </TabsWithBadge>
        </CardContent>
      </Card>
    </div>
  );
}
