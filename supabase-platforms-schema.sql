-- FAZA 2D: <PERSON>reiranje schema za platforme i content tipove
-- Pokrenuti u Supabase SQL Editor

-- 1. Kreiranje platforms tabele
CREATE TABLE IF NOT EXISTS platforms (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  slug VARCHAR(50) NOT NULL UNIQUE,
  icon VARCHAR(10),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Umetanje predefinisanih platformi
INSERT INTO platforms (name, slug, icon) VALUES
('Instagram', 'instagram', '📷'),
('TikTok', 'tiktok', '🎵'),
('YouTube', 'youtube', '📺'),
('Facebook', 'facebook', '👥'),
('Twitter/X', 'twitter', '🐦')
ON CONFLICT (slug) DO NOTHING;

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> content_types tabele
CREATE TABLE IF NOT EXISTS content_types (
  id SERIAL PRIMARY KEY,
  platform_id INTEGER REFERENCES platforms(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(platform_id, slug)
);

-- Umetanje content tipova po platformama
INSERT INTO content_types (platform_id, name, slug, description) VALUES
-- Instagram (platform_id = 1)
(1, 'Photo Feed Post', 'photo-feed-post', 'Obična fotografija u Instagram feed-u'),
(1, 'Reel', 'reel', 'Kratki video u Instagram Reels sekciji'),
(1, 'Story', 'story', 'Instagram Story koji traje 24 sata'),
(1, 'Live Stream', 'live-stream', 'Instagram Live prenos'),
(1, 'IGTV', 'igtv', 'Duži video sadržaj na IGTV'),

-- TikTok (platform_id = 2)
(2, 'Video Post', 'video-post', 'Standardni TikTok video'),
(2, 'Live Stream', 'live-stream', 'TikTok Live prenos'),

-- YouTube (platform_id = 3)
(3, 'Short Video', 'short-video', 'YouTube video kraći od 1 minute'),
(3, 'Long Video', 'long-video', 'YouTube video duži od 1 minute'),
(3, 'YouTube Shorts', 'youtube-shorts', 'Kratki vertikalni video'),
(3, 'Live Stream', 'live-stream', 'YouTube Live prenos'),

-- Facebook (platform_id = 4)
(4, 'Photo Post', 'photo-post', 'Facebook fotografija'),
(4, 'Video Post', 'video-post', 'Facebook video'),
(4, 'Story', 'story', 'Facebook Story'),
(4, 'Live Stream', 'live-stream', 'Facebook Live'),

-- Twitter/X (platform_id = 5)
(5, 'Tweet', 'tweet', 'Standardni tweet'),
(5, 'Thread', 'thread', 'Twitter thread'),
(5, 'Space', 'space', 'Twitter Space audio')
ON CONFLICT (platform_id, slug) DO NOTHING;

-- 3. Kreiranje influencer_platforms tabele (koje platforme koristi)
CREATE TABLE IF NOT EXISTS influencer_platforms (
  influencer_id UUID REFERENCES influencers(id) ON DELETE CASCADE,
  platform_id INTEGER REFERENCES platforms(id) ON DELETE CASCADE,
  handle VARCHAR(100),
  followers_count INTEGER DEFAULT 0,
  is_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (influencer_id, platform_id)
);

-- 4. Kreiranje influencer_platform_pricing tabele (cijene po content tipu)
CREATE TABLE IF NOT EXISTS influencer_platform_pricing (
  id SERIAL PRIMARY KEY,
  influencer_id UUID REFERENCES influencers(id) ON DELETE CASCADE,
  platform_id INTEGER REFERENCES platforms(id) ON DELETE CASCADE,
  content_type_id INTEGER REFERENCES content_types(id) ON DELETE CASCADE,
  price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
  currency VARCHAR(3) DEFAULT 'EUR',
  is_available BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(influencer_id, platform_id, content_type_id)
);

-- 5. Kreiranje indeksa za performanse
CREATE INDEX IF NOT EXISTS idx_platforms_slug ON platforms(slug);
CREATE INDEX IF NOT EXISTS idx_platforms_active ON platforms(is_active);

CREATE INDEX IF NOT EXISTS idx_content_types_platform ON content_types(platform_id);
CREATE INDEX IF NOT EXISTS idx_content_types_slug ON content_types(slug);
CREATE INDEX IF NOT EXISTS idx_content_types_active ON content_types(is_active);

CREATE INDEX IF NOT EXISTS idx_influencer_platforms_influencer ON influencer_platforms(influencer_id);
CREATE INDEX IF NOT EXISTS idx_influencer_platforms_platform ON influencer_platforms(platform_id);
CREATE INDEX IF NOT EXISTS idx_influencer_platforms_active ON influencer_platforms(is_active);

CREATE INDEX IF NOT EXISTS idx_pricing_influencer ON influencer_platform_pricing(influencer_id);
CREATE INDEX IF NOT EXISTS idx_pricing_platform ON influencer_platform_pricing(platform_id);
CREATE INDEX IF NOT EXISTS idx_pricing_content_type ON influencer_platform_pricing(content_type_id);
CREATE INDEX IF NOT EXISTS idx_pricing_price ON influencer_platform_pricing(price);
CREATE INDEX IF NOT EXISTS idx_pricing_available ON influencer_platform_pricing(is_available);

-- 6. RLS (Row Level Security) policies
ALTER TABLE platforms ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE influencer_platforms ENABLE ROW LEVEL SECURITY;
ALTER TABLE influencer_platform_pricing ENABLE ROW LEVEL SECURITY;

-- RLS policies za platforms (svi mogu čitati)
CREATE POLICY "Anyone can view platforms" ON platforms FOR SELECT USING (true);

-- RLS policies za content_types (svi mogu čitati)
CREATE POLICY "Anyone can view content types" ON content_types FOR SELECT USING (true);

-- RLS policies za influencer_platforms
CREATE POLICY "Anyone can view influencer platforms" ON influencer_platforms FOR SELECT USING (true);
CREATE POLICY "Influencers can manage own platforms" ON influencer_platforms 
  FOR ALL USING (auth.uid() = influencer_id);

-- RLS policies za influencer_platform_pricing
CREATE POLICY "Anyone can view pricing" ON influencer_platform_pricing FOR SELECT USING (true);
CREATE POLICY "Influencers can manage own pricing" ON influencer_platform_pricing 
  FOR ALL USING (auth.uid() = influencer_id);

-- 7. Trigger za updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_influencer_platforms_updated_at
  BEFORE UPDATE ON influencer_platforms
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pricing_updated_at
  BEFORE UPDATE ON influencer_platform_pricing
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. Helper funkcije
CREATE OR REPLACE FUNCTION get_platforms_with_content_types()
RETURNS TABLE (
  platform_id INTEGER,
  platform_name VARCHAR(50),
  platform_slug VARCHAR(50),
  platform_icon VARCHAR(10),
  content_types JSON
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id as platform_id,
    p.name as platform_name,
    p.slug as platform_slug,
    p.icon as platform_icon,
    COALESCE(
      JSON_AGG(
        JSON_BUILD_OBJECT(
          'id', ct.id,
          'name', ct.name,
          'slug', ct.slug,
          'description', ct.description
        ) ORDER BY ct.name
      ) FILTER (WHERE ct.id IS NOT NULL),
      '[]'::json
    ) as content_types
  FROM platforms p
  LEFT JOIN content_types ct ON p.id = ct.platform_id AND ct.is_active = true
  WHERE p.is_active = true
  GROUP BY p.id, p.name, p.slug, p.icon
  ORDER BY p.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. Funkcija za pretragu influencera po platformi i content tipu
CREATE OR REPLACE FUNCTION search_influencers_by_platform_content(
  target_platform_id INTEGER DEFAULT NULL,
  target_content_type_id INTEGER DEFAULT NULL,
  min_price DECIMAL DEFAULT NULL,
  max_price DECIMAL DEFAULT NULL
)
RETURNS TABLE (
  influencer_id UUID,
  platform_id INTEGER,
  content_type_id INTEGER,
  price DECIMAL,
  handle VARCHAR,
  followers_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ipp.influencer_id,
    ipp.platform_id,
    ipp.content_type_id,
    ipp.price,
    ip.handle,
    ip.followers_count
  FROM influencer_platform_pricing ipp
  JOIN influencer_platforms ip ON ipp.influencer_id = ip.influencer_id 
    AND ipp.platform_id = ip.platform_id
  WHERE 
    ipp.is_available = true
    AND ip.is_active = true
    AND (target_platform_id IS NULL OR ipp.platform_id = target_platform_id)
    AND (target_content_type_id IS NULL OR ipp.content_type_id = target_content_type_id)
    AND (min_price IS NULL OR ipp.price >= min_price)
    AND (max_price IS NULL OR ipp.price <= max_price)
  ORDER BY ipp.price ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
