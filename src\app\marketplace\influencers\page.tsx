'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  searchInfluencers,
  type InfluencerSearchResult,
} from '@/lib/marketplace';
import { TooltipProvider } from '@/components/ui/tooltip';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { InfluencerCard } from '@/components/marketplace/InfluencerCard';

export default function InfluencerMarketplacePage() {
  const [influencers, setInfluencers] = useState<InfluencerSearchResult[]>([]);
  const [loading, setLoading] = useState(true);

  // Load initial data
  useEffect(() => {
    loadInfluencers();
  }, []);

  const loadInfluencers = async () => {
    setLoading(true);
    try {
      const { data, error } = await searchInfluencers({});

      console.log('Search result:', { data, error });

      if (error) {
        console.error('Error loading influencers:', error);
        return;
      }

      console.log('Setting influencers:', data?.length || 0);
      setInfluencers(data || []);
    } catch (error) {
      console.error('Error in loadInfluencers:', error);
    } finally {
      setLoading(false);
    }
  };



  return (
    <DashboardLayout requiredUserType="business">
      <TooltipProvider>
        <div className="space-y-6">
          {/* Header */}
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">
              Marketplace Influencera
            </h1>
            <p className="text-muted-foreground mt-1">
              Pronađite savršenog influencera za vašu kampanju
            </p>
          </div>

          <div>

            {/* Results */}
            <div className="w-full">
              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-16 h-16 bg-muted rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-4 bg-muted rounded mb-2"></div>
                            <div className="h-3 bg-muted rounded w-2/3"></div>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="h-3 bg-muted rounded"></div>
                          <div className="h-3 bg-muted rounded w-3/4"></div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : influencers.length === 0 ? (
                <Card>
                  <CardContent className="p-12 text-center">
                    <h3 className="text-lg font-semibold mb-2">
                      Nema rezultata
                    </h3>
                    <p className="text-muted-foreground">
                      Trenutno nema dostupnih influencera.
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-semibold">
                      {influencers.length} influencer
                      {influencers.length !== 1 ? 'a' : ''}
                    </h2>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                    {influencers.map(influencer => (
                      <InfluencerCard key={influencer.id} influencer={influencer} filters={{}} />
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </TooltipProvider>
    </DashboardLayout>
  );
}
