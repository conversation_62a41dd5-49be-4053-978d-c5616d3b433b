import { supabase } from './supabase';
import { upsertApplicationChatPermission } from './chat-permissions';
import { Database } from './database.types';
import {
  Campaign,
  CampaignWithBusiness,
  CampaignDetails,
  CampaignApplication,
  ApplicationWithDetails,
  InfluencerApplicationResponse,
  CampaignFormData,
  ApplicationFormData,
  Business,
  Profile,
  Platform,
  Category,
} from './types';

type CampaignInsert = Database['public']['Tables']['campaigns']['Insert'];
type CampaignUpdate = Database['public']['Tables']['campaigns']['Update'];
type CampaignApplicationInsert =
  Database['public']['Tables']['campaign_applications']['Insert'];

// Campaign CRUD operations
export async function createCampaign(campaign: CampaignInsert) {
  const { data, error } = await supabase
    .from('campaigns')
    .insert(campaign)
    .select();

  // Return first item if data is array, otherwise return as is
  const result = data && Array.isArray(data) ? data[0] : data;

  return { data: result, error };
}

export async function getCampaign(id: string) {
  const { data, error } = await supabase
    .from('campaigns')
    .select(
      `
      *,
      businesses!inner(
        company_name,
        industry,
        profiles!inner(
          username,
          avatar_url
        )
      )
    `
    )
    .eq('id', id)
    .single();

  return { data, error };
}

export async function getCampaignWithDetails(
  id: string
): Promise<{ data: CampaignDetails | null; error: any }> {
  const { data, error } = await supabase
    .from('campaigns')
    .select(
      `
      *,
      businesses!inner(
        *,
        profiles!inner(*)
      )
    `
    )
    .eq('id', id)
    .single();

  if (error) return { data: null, error };

  // Get campaign platforms
  const { data: platformsData, error: platformsError } = await supabase
    .from('campaign_platforms')
    .select(`
      content_types,
      platforms!inner(
        id,
        name,
        slug,
        icon
      )
    `)
    .eq('campaign_id', id);

  const platforms = platformsData?.map(cp => ({
    ...cp.platforms,
    content_types: cp.content_types
  })) || [];

  // Get campaign categories
  const { data: categoriesData, error: categoriesError } = await supabase
    .from('campaign_categories')
    .select(`
      categories!inner(
        id,
        name,
        icon
      )
    `)
    .eq('campaign_id', id);

  const categories = categoriesData?.map(cc => cc.categories) || [];

  // Transform data to match CampaignDetails interface
  const transformedData: CampaignDetails = {
    ...data,
    business: data.businesses
      ? {
          ...data.businesses,
          profile: data.businesses.profiles,
        }
      : undefined,
    platforms: platforms,
    categories: categories,
    // Flatten business profile fields for easier access
    company_name: data.businesses?.company_name,
    business_username: data.businesses?.profiles?.username,
    business_avatar: data.businesses?.profiles?.avatar_url,
    industry: data.businesses?.industry,
  };

  return { data: transformedData, error: null };
}

export async function deleteCampaign(id: string) {
  const { data, error } = await supabase
    .from('campaigns')
    .delete()
    .eq('id', id);

  return { data, error };
}

// Business campaigns
export async function getBusinessCampaigns(
  businessId: string,
  status?: string
) {
  let query = supabase
    .from('campaigns')
    .select(
      `
      *,
      campaign_applications(count)
    `
    )
    .eq('business_id', businessId)
    .order('created_at', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error } = await query;
  return { data, error };
}

// Campaign search and filtering for influencers
export interface CampaignFilters {
  search?: string;
  categories?: number[];
  platforms?: number[];
  minBudget?: number;
  maxBudget?: number;
  location?: string;
  minFollowers?: number;
  maxFollowers?: number;
  gender?: string;
  deadlineBefore?: string;
  sortBy?:
    | 'created_at'
    | 'budget'
    | 'application_deadline'
    | 'applications_count';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export async function searchCampaigns(filters: CampaignFilters = {}) {
  const {
    search,
    categories,
    platforms,
    minBudget,
    maxBudget,
    location,
    minFollowers,
    maxFollowers,
    gender,
    deadlineBefore,
    sortBy = 'created_at',
    sortOrder = 'desc',
    limit = 20,
    offset = 0,
  } = filters;

  // Get current user for debugging
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Get active campaigns directly with proper RLS
  const { data: allCampaigns, error: campaignsError } = await supabase
    .from('campaigns')
    .select(
      `
      id,
      title,
      description,
      budget,
      status,
      created_at,
      business_id,
      content_types,
      location,
      application_deadline
    `
    )
    .eq('status', 'active')
    .order('created_at', { ascending: false });

  if (campaignsError) {
    console.error('Campaigns error:', campaignsError);
    return { data: [], error: campaignsError };
  }

  // Apply client-side filtering
  let filteredCampaigns = allCampaigns || [];

  // Basic text search in title and description
  if (search) {
    const searchLower = search.toLowerCase();
    filteredCampaigns = filteredCampaigns.filter(
      campaign =>
        campaign.title.toLowerCase().includes(searchLower) ||
        campaign.description.toLowerCase().includes(searchLower)
    );
  }

  // Budget range
  if (minBudget !== undefined) {
    filteredCampaigns = filteredCampaigns.filter(
      campaign => campaign.budget >= minBudget
    );
  }
  if (maxBudget !== undefined) {
    filteredCampaigns = filteredCampaigns.filter(
      campaign => campaign.budget <= maxBudget
    );
  }

  // Skip advanced filters for now since RPC returns basic fields only
  // TODO: Add these filters back when we have all fields in RPC

  // Sorting
  filteredCampaigns.sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'budget':
        aValue = a.budget;
        bValue = b.budget;
        break;
      default: // 'created_at'
        aValue = new Date(a.created_at).getTime();
        bValue = new Date(b.created_at).getTime();
    }

    return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
  });

  // Pagination
  const startIndex = offset;
  const endIndex = offset + limit;
  const paginatedCampaigns = filteredCampaigns.slice(startIndex, endIndex);

  // Get platforms for each campaign
  const campaignsWithPlatforms = await Promise.all(
    paginatedCampaigns.map(async (campaign) => {
      const { data: platformsData } = await supabase
        .from('campaign_platforms')
        .select(`
          content_types,
          platforms!inner(
            id,
            name,
            slug,
            icon
          )
        `)
        .eq('campaign_id', campaign.id);

      const platforms = platformsData?.map(cp => ({
        ...cp.platforms,
        content_types: cp.content_types
      })) || [];
      return { ...campaign, platforms };
    })
  );

  return { data: campaignsWithPlatforms, error: null };
}

// Get campaign for editing (only for business owners and draft campaigns)
export async function getCampaignForEdit(campaignId: string) {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // Get campaign with all related data
  const { data: campaign, error } = await supabase
    .from('campaigns')
    .select(
      `
      *,
      campaign_platforms (
        platform_id,
        platforms (name)
      ),
      campaign_categories (
        category_id,
        categories (name)
      )
    `
    )
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .eq('status', 'draft')
    .single();

  if (error) {
    return { data: null, error };
  }

  if (!campaign) {
    return {
      data: null,
      error: { message: 'Campaign not found or not editable' },
    };
  }

  return { data: campaign, error: null };
}

// Update campaign (only for business owners and draft campaigns)
export async function updateCampaign(campaignId: string, campaignData: any) {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // First check if campaign exists and is editable
  const { data: existingCampaign, error: checkError } = await supabase
    .from('campaigns')
    .select('id, status, business_id')
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .eq('status', 'draft')
    .single();

  if (checkError || !existingCampaign) {
    return {
      data: null,
      error: { message: 'Campaign not found or not editable' },
    };
  }

  // Update campaign
  const { data: updatedCampaign, error: updateError } = await supabase
    .from('campaigns')
    .update({
      title: campaignData.title,
      description: campaignData.description,
      budget: campaignData.budget,
      content_types: campaignData.content_types,
      min_followers: campaignData.min_followers,
      max_followers: campaignData.max_followers,
      age_range_min: campaignData.age_range_min,
      age_range_max: campaignData.age_range_max,
      gender: campaignData.gender,
      updated_at: new Date().toISOString(),
    })
    .eq('id', campaignId)
    .select()
    .single();

  if (updateError) {
    return { data: null, error: updateError };
  }

  // Update platforms
  if (campaignData.platforms && campaignData.platforms.length > 0) {
    // Delete existing platforms
    await supabase
      .from('campaign_platforms')
      .delete()
      .eq('campaign_id', campaignId);

    // Insert new platforms
    const platformInserts = campaignData.platforms.map(
      (platformId: string) => ({
        campaign_id: campaignId,
        platform_id: platformId,
      })
    );

    await supabase.from('campaign_platforms').insert(platformInserts);
  }

  // Update categories
  if (campaignData.categories && campaignData.categories.length > 0) {
    // Delete existing categories
    await supabase
      .from('campaign_categories')
      .delete()
      .eq('campaign_id', campaignId);

    // Insert new categories
    const categoryInserts = campaignData.categories.map(
      (categoryId: string) => ({
        campaign_id: campaignId,
        category_id: categoryId,
      })
    );

    await supabase.from('campaign_categories').insert(categoryInserts);
  }

  return { data: updatedCampaign, error: null };
}

// Update campaign status (with validation)
export async function updateCampaignStatus(
  campaignId: string,
  newStatus: 'draft' | 'active' | 'paused' | 'completed'
) {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return { data: null, error: { message: 'User not authenticated' } };
  }

  // First check if campaign exists and user owns it
  const { data: existingCampaign, error: checkError } = await supabase
    .from('campaigns')
    .select('id, status, business_id')
    .eq('id', campaignId)
    .eq('business_id', user.id)
    .single();

  if (checkError || !existingCampaign) {
    return {
      data: null,
      error: { message: 'Campaign not found or access denied' },
    };
  }

  // Validation rules
  if (existingCampaign.status === 'active' && newStatus === 'draft') {
    return {
      data: null,
      error: { message: 'Cannot change active campaign back to draft' },
    };
  }

  if (existingCampaign.status === 'completed') {
    return {
      data: null,
      error: { message: 'Cannot change status of completed campaign' },
    };
  }

  // Update status
  const { data: updatedCampaign, error: updateError } = await supabase
    .from('campaigns')
    .update({
      status: newStatus,
      updated_at: new Date().toISOString(),
    })
    .eq('id', campaignId)
    .select()
    .single();

  if (updateError) {
    return { data: null, error: updateError };
  }

  return { data: updatedCampaign, error: null };
}

// Get business campaigns for dashboard with counts
export async function getBusinessCampaignsForDashboard() {
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    console.log('User not authenticated');
    return { data: [], error: { message: 'User not authenticated' } };
  }

  console.log('Loading campaigns for business:', user.id);

  const { data: campaigns, error } = await supabase
    .from('campaigns')
    .select(
      `
      id,
      title,
      description,
      budget,
      status,
      created_at,
      content_types,
      campaign_applications(count)
    `
    )
    .eq('business_id', user.id)
    .order('created_at', { ascending: false });

  console.log('Campaigns query result:', { campaigns, error });

  if (error) {
    console.error('Error loading campaigns:', error);
    return { data: [], error };
  }

  return { data: campaigns || [], error: null };
}

// Featured campaigns - return campaigns marked as featured
export async function getFeaturedCampaigns(limit: number = 6) {
  const { data, error } = await supabase
    .from('campaigns')
    .select('*')
    .eq('status', 'active')
    .eq('is_featured', true)
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Featured campaigns error:', error);
    return { data: [], error };
  }

  // Get platforms for each featured campaign
  const campaignsWithPlatforms = await Promise.all(
    (data || []).map(async (campaign) => {
      const { data: platformsData } = await supabase
        .from('campaign_platforms')
        .select(`
          content_types,
          platforms!inner(
            id,
            name,
            slug,
            icon
          )
        `)
        .eq('campaign_id', campaign.id);

      const platforms = platformsData?.map(cp => ({
        ...cp.platforms,
        content_types: cp.content_types
      })) || [];
      return { ...campaign, platforms };
    })
  );

  return { data: campaignsWithPlatforms, error: null };
}

// Campaign applications
export async function createCampaignApplication(
  application: CampaignApplicationInsert
) {
  const { data, error } = await supabase
    .from('campaign_applications')
    .insert(application)
    .select()
    .single();

  return { data, error };
}

// Check if influencer already applied to campaign
export async function hasInfluencerApplied(
  campaignId: string,
  influencerId: string
): Promise<{ data: InfluencerApplicationResponse | null; error: any }> {
  const { data, error } = await supabase
    .from('campaign_applications')
    .select('*')
    .eq('campaign_id', campaignId)
    .eq('influencer_id', influencerId)
    .single();

  if (error && error.code !== 'PGRST116') {
    return { data: null, error };
  }

  const response: InfluencerApplicationResponse = {
    hasApplied: !!data,
    application: data || undefined,
  };

  return { data: response, error: null };
}

export async function getCampaignApplications(
  campaignId: string,
  status?: string
) {
  let query = supabase
    .from('campaign_applications')
    .select(
      `
      *,
      influencers!inner(
        *,
        profiles!inner(
          username,
          full_name,
          public_display_name,
          avatar_url,
          bio,
          city,
          country,
          age,
          gender
        )
      )
    `
    )
    .eq('campaign_id', campaignId)
    .order('applied_at', { ascending: false });

  if (status) {
    query = query.eq('status', status);
  }

  const { data, error } = await query;
  return { data, error };
}

export async function getInfluencerApplications(
  influencerId: string,
  status?: string
) {
  try {
    console.log('Fetching applications for influencer:', influencerId);

    let query = supabase
      .from('campaign_applications')
      .select(
        `
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_rate,
        proposal_text,
        delivery_timeframe,
        portfolio_links,
        additional_services,
        available_start_date,
        experience_relevant,
        audience_insights,
        applied_at,
        campaigns!inner(
          id,
          title,
          description,
          budget,
          status,
          business_id
        )
      `
      )
      .eq('influencer_id', influencerId)
      .order('applied_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data: applicationsData, error: applicationsError } = await query;

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError);
      throw applicationsError;
    }

    console.log('Applications data:', applicationsData);

    if (!applicationsData || applicationsData.length === 0) {
      return { data: [], error: null };
    }

    // Get business data separately
    const businessIds = applicationsData.map(app => app.campaigns.business_id);
    const { data: businessesData, error: businessesError } = await supabase
      .from('businesses')
      .select(
        `
        id,
        company_name
      `
      )
      .in('id', businessIds);

    if (businessesError) {
      console.error('Error fetching businesses:', businessesError);
      throw businessesError;
    }

    // Get business profiles
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('id, username, avatar_url')
      .in('id', businessIds);

    if (profilesError) {
      console.error('Error fetching business profiles:', profilesError);
    }

    console.log('Businesses data:', businessesData);
    console.log('Profiles data:', profilesData);

    // Transform data to match expected interface
    const transformedData = applicationsData.map(app => {
      const business = businessesData?.find(
        b => b.id === app.campaigns.business_id
      );
      const profile = profilesData?.find(
        p => p.id === app.campaigns.business_id
      );

      return {
        id: app.id,
        campaign_id: app.campaign_id,
        status: app.status,
        proposed_rate: app.proposed_rate,
        proposal_text: app.proposal_text,
        portfolio_links: app.portfolio_links,
        delivery_timeframe: app.delivery_timeframe,
        additional_services: app.additional_services,
        applied_at: app.applied_at,
        campaigns: {
          title: app.campaigns.title,
          description: app.campaigns.description,
          budget: app.campaigns.budget,
          status: app.campaigns.status,
          businesses: {
            company_name: business?.company_name || 'Unknown Company',
            profiles: {
              username: profile?.username || 'Unknown',
              avatar_url: profile?.avatar_url || null,
            },
          },
        },
      };
    });

    return { data: transformedData, error: null };
  } catch (error: any) {
    console.error('Error in getInfluencerApplications:', error);
    return { data: null, error: error.message };
  }
}

// Campaign platforms and categories
export async function addCampaignPlatforms(
  campaignId: string,
  platforms: Array<{
    platform_id: number;
    content_type_ids: number[];
    posts_required?: number;
    budget_per_post?: number;
  }>
) {
  const platformData = platforms.map(platform => ({
    campaign_id: campaignId,
    ...platform,
  }));

  const { data, error } = await supabase
    .from('campaign_platforms')
    .insert(platformData)
    .select();

  return { data, error };
}

export async function addCampaignCategories(
  campaignId: string,
  categoryIds: number[]
) {
  const categoryData = categoryIds.map(categoryId => ({
    campaign_id: campaignId,
    category_id: categoryId,
  }));

  const { data, error } = await supabase
    .from('campaign_categories')
    .insert(categoryData)
    .select();

  return { data, error };
}

export async function removeCampaignPlatforms(campaignId: string) {
  const { data, error } = await supabase
    .from('campaign_platforms')
    .delete()
    .eq('campaign_id', campaignId);

  return { data, error };
}

export async function removeCampaignCategories(campaignId: string) {
  const { data, error } = await supabase
    .from('campaign_categories')
    .delete()
    .eq('campaign_id', campaignId);

  return { data, error };
}

// Utility functions

export async function refreshCampaignsSearchView() {
  const { data, error } = await supabase.rpc('refresh_campaigns_search_view');

  return { data, error };
}

// Get campaign statistics for business dashboard
export async function getCampaignStats(businessId: string) {
  const { data: campaigns, error: campaignsError } = await supabase
    .from('campaigns')
    .select('status')
    .eq('business_id', businessId);

  if (campaignsError) return { data: null, error: campaignsError };

  const { data: applications, error: applicationsError } = await supabase
    .from('campaign_applications')
    .select('status, campaign_id')
    .in('campaign_id', campaigns?.map(c => c.id) || []);

  if (applicationsError) return { data: null, error: applicationsError };

  const stats = {
    totalCampaigns: campaigns?.length || 0,
    activeCampaigns: campaigns?.filter(c => c.status === 'active').length || 0,
    completedCampaigns:
      campaigns?.filter(c => c.status === 'completed').length || 0,
    totalApplications: applications?.length || 0,
    pendingApplications:
      applications?.filter(a => a.status === 'pending').length || 0,
    acceptedApplications:
      applications?.filter(a => a.status === 'accepted').length || 0,
  };

  return { data: stats, error: null };
}

// Get all applications for business campaigns
export async function getBusinessCampaignApplications(
  businessId: string,
  status?: string
) {
  try {
    console.log('Fetching applications for business:', businessId);

    // Use the correct field names from the actual database schema
    let query = supabase
      .from('campaign_applications')
      .select(
        `
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_rate,
        proposal_text,
        delivery_timeframe,
        portfolio_links,
        additional_services,
        available_start_date,
        experience_relevant,
        audience_insights,
        applied_at,
        campaigns!inner(
          id,
          title,
          budget,
          business_id
        )
      `
      )
      .eq('campaigns.business_id', businessId)
      .order('applied_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data: applicationsData, error: applicationsError } = await query;

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError);
      throw applicationsError;
    }

    console.log('Applications data:', applicationsData);

    if (!applicationsData || applicationsData.length === 0) {
      return { data: [], error: null };
    }

    // Get influencer data with profiles joined
    const influencerIds = applicationsData.map(app => app.influencer_id);
    const { data: influencersData, error: influencersError } = await supabase
      .from('influencers')
      .select(
        `
        id,
        profiles!inner(
          id,
          username,
          full_name,
          public_display_name,
          avatar_url
        )
      `
      )
      .in('id', influencerIds);

    if (influencersError) {
      console.error('Error fetching influencers:', influencersError);
      throw influencersError;
    }

    console.log('Influencers data:', influencersData);

    // Transform data to match expected interface
    const transformedData = applicationsData?.map(app => {
      const influencer = influencersData?.find(
        inf => inf.id === app.influencer_id
      );
      return {
        id: app.id,
        campaign_id: app.campaign_id,
        influencer_id: app.influencer_id,
        status: app.status,
        proposed_rate: app.proposed_rate,
        proposal_text: app.proposal_text,
        delivery_timeframe: app.delivery_timeframe || 'Nije specificirano',
        portfolio_links: app.portfolio_links || [],
        experience_relevant: app.experience_relevant || app.proposal_text,
        audience_insights: app.audience_insights || '',
        additional_services: app.additional_services || '',
        available_start_date: app.available_start_date,
        applied_at: app.applied_at,
        campaigns: {
          id: app.campaigns.id,
          title: app.campaigns.title,
          budget: app.campaigns.budget,
          business_id: app.campaigns.business_id,
        },
        profiles: {
          id: influencer?.profiles?.id || app.influencer_id,
          username: influencer?.profiles?.username || 'Unknown',
          full_name: influencer?.profiles?.full_name || 'Unknown User',
          public_display_name: influencer?.profiles?.public_display_name || null,
          avatar_url: influencer?.profiles?.avatar_url || null,
        },
      };
    });

    return { data: transformedData, error: null };
  } catch (error: any) {
    console.error('Error fetching campaign applications:', error);
    return { data: null, error: error.message };
  }
}

// Get single application with detailed info
export async function getCampaignApplication(
  applicationId: string
): Promise<{ data: ApplicationWithDetails | null; error: any }> {
  try {
    const { data, error } = await supabase
      .from('campaign_applications')
      .select(
        `
        id,
        campaign_id,
        influencer_id,
        status,
        proposed_rate,
        proposal_text,
        portfolio_links,
        delivery_timeframe,
        additional_services,
        experience_relevant,
        audience_insights,
        applied_at,
        campaigns!inner(
          id,
          title,
          description,
          budget,
          requirements,
          deliverables,
          business_id,
          hashtags,
          do_not_mention,
          additional_notes,
          start_date,
          end_date
        ),
        influencers!campaign_applications_influencer_id_fkey(
          id,
          profiles!influencers_id_fkey(
            id,
            full_name,
            public_display_name,
            username,
            avatar_url,
            bio,
            city,
            country,
            age,
            gender
          )
        )
      `
      )
      .eq('id', applicationId)
      .single();

    if (error) throw error;

    // Get campaign platforms
    const { data: campaignPlatformsData } = await supabase
      .from('campaign_platforms')
      .select(`
        content_types,
        platforms!inner(
          id,
          name,
          slug,
          icon
        )
      `)
      .eq('campaign_id', data.campaigns.id);

    const campaignPlatforms = campaignPlatformsData?.map(cp => ({
      ...cp.platforms,
      content_types: cp.content_types
    })) || [];

    // Get campaign categories
    const { data: campaignCategoriesData } = await supabase
      .from('campaign_categories')
      .select('categories(id, name, icon)')
      .eq('campaign_id', data.campaigns.id);

    const campaignCategories = campaignCategoriesData?.map(cc => cc.categories) || [];

    // Get influencer categories and platforms
    const { data: categoriesData } = await supabase
      .from('influencer_categories')
      .select('categories(name)')
      .eq('influencer_id', data.influencer_id);

    const { data: platformsData } = await supabase
      .from('influencer_platforms')
      .select(
        `
        handle,
        followers_count,
        platforms(name)
      `
      )
      .eq('influencer_id', data.influencer_id);

    // Transform categories and platforms data
    const categories = categoriesData?.map(cat => cat.categories.name) || [];
    const platforms = platformsData?.map(platform => ({
      platform_name: platform.platforms.name,
      handle: platform.handle,
      followers_count: platform.followers_count,
    })) || [];

    // Transform data to match ApplicationWithDetails interface
    const transformedData: ApplicationWithDetails = {
      ...data,
      campaign: {
        ...data.campaigns,
        business: undefined, // Will need to fetch separately if needed
        platforms: campaignPlatforms,
        categories: campaignCategories,
      },
      influencer: {
        ...data.influencers,
        profile: data.influencers.profiles,
        categories,
        platforms,
      },
    };

    return { data: transformedData, error: null };
  } catch (error: any) {
    console.error('Error fetching campaign application:', error);
    return { data: null, error: error.message };
  }
}

// Update application status
export async function updateApplicationStatus(
  applicationId: string,
  status: 'accepted' | 'rejected',
  rejectionReason?: string
) {
  try {
    const updateData: any = { status };

    if (status === 'rejected' && rejectionReason) {
      updateData.rejection_reason = rejectionReason;
    }

    const { data, error } = await supabase
      .from('campaign_applications')
      .update(updateData)
      .eq('id', applicationId)
      .select(
        `
        *,
        campaigns!inner(business_id)
      `
      )
      .single();

    if (error) throw error;

    // Kreiraj chat dozvolu kada se aplikacija prihvati
    if (status === 'accepted' && data) {
      try {
        // Business je odobrio prihvatanjem aplikacije - omogući chat odmah
        await upsertApplicationChatPermission(
          data.campaigns.business_id,
          data.influencer_id,
          data.id,
          true, // business_approved - business je odobrio prihvatanjem
          true // influencer_approved - omogući chat odmah
        );
      } catch (chatError) {
        console.error('Error creating chat permission:', chatError);
        // Ne prekidamo proces ako chat dozvola ne uspije
      }
    }

    // Pošalji notifikaciju influenceru
    try {
      const { data: campaignData } = await supabase
        .from('campaigns')
        .select('title')
        .eq('id', data.campaign_id)
        .single();

      const { data: businessData } = await supabase
        .from('profiles')
        .select('full_name, username')
        .eq('id', data.campaigns.business_id)
        .single();

      const notificationType =
        status === 'accepted' ? 'campaign_accepted' : 'campaign_rejected';
      const notificationTitle =
        status === 'accepted' ? 'Aplikacija prihvaćena' : 'Aplikacija odbačena';
      const notificationMessage =
        status === 'accepted'
          ? `${businessData?.full_name || businessData?.username || 'Biznis'} je prihvatio vašu aplikaciju za kampanju "${campaignData?.title || 'Nepoznata kampanja'}"`
          : `${businessData?.full_name || businessData?.username || 'Biznis'} je odbacio vašu aplikaciju za kampanju "${campaignData?.title || 'Nepoznata kampanja'}"${rejectionReason ? `. Razlog: ${rejectionReason}` : ''}`;

      await supabase.rpc('create_notification', {
        p_user_id: data.influencer_id,
        p_type: notificationType,
        p_title: notificationTitle,
        p_message: notificationMessage,
        p_data: {
          campaign_application_id: applicationId,
          campaign_id: data.campaign_id,
          business_name: businessData?.full_name || businessData?.username,
          campaign_title: campaignData?.title,
          rejection_reason: rejectionReason,
        },
      });
    } catch (notificationError) {
      console.error('Error creating notification:', notificationError);
      // Ne prekidamo proces ako notifikacija ne uspije
    }

    return { data, error: null };
  } catch (error: any) {
    console.error('Error updating application status:', error);
    return { data: null, error: error.message };
  }
}
