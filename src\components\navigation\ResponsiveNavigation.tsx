'use client';

import { MobileBottomNavigation } from './MobileBottomNavigation';
import { MobileTopNavbar } from './MobileTopNavbar';

interface ResponsiveNavigationProps {
  userType: 'influencer' | 'business';
  profile: any;
  showBackButton?: boolean;
  onBackClick?: () => void;
}

export function ResponsiveNavigation({ userType, profile, showBackButton, onBackClick }: ResponsiveNavigationProps) {
  return (
    <>
      {/* Mobile Top Navbar - prikazuje se samo na mobile uređajima */}
      <MobileTopNavbar
        userType={userType}
        profile={profile}
        showBackButton={showBackButton}
        onBackClick={onBackClick}
      />

      {/* Mobile Bottom Navigation - prikazuje se samo na mobile uređajima */}
      <MobileBottomNavigation userType={userType} />
    </>
  );
}
