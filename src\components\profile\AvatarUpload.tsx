'use client';

import React, { useState, useRef, useCallback } from 'react';
import { Upload, X, Camera, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';
import { 
  uploadAvatarWithCompression, 
  type UploadProgress,
  type AvatarUrls 
} from '@/lib/avatar-upload';
import { 
  validateImageFile, 
  createImagePreview, 
  cleanupImagePreview,
  formatFileSize 
} from '@/lib/image-compression';
import Image from 'next/image';

interface AvatarUploadProps {
  userId: string;
  currentAvatarUrl?: string;
  onUploadComplete?: (avatarUrls: AvatarUrls) => void;
  onUploadError?: (error: string) => void;
  className?: string;
}

export function AvatarUpload({
  userId,
  currentAvatarUrl,
  onUploadComplete,
  onUploadError,
  className = '',
}: AvatarUploadProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [compressionStats, setCompressionStats] = useState<{
    originalSize: number;
    totalCompressedSize: number;
    compressionRatio: number;
  } | null>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelection(files[0]);
    }
  }, []);

  const handleFileSelection = (file: File) => {
    setError(null);
    setSuccess(false);
    
    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      setError(validation.error || 'Neispravna slika');
      return;
    }

    // Clean up previous preview
    if (previewUrl) {
      cleanupImagePreview(previewUrl);
    }

    // Set new file and preview
    setSelectedFile(file);
    const newPreviewUrl = createImagePreview(file);
    setPreviewUrl(newPreviewUrl);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setError(null);
    setUploadProgress(null);

    try {
      const result = await uploadAvatarWithCompression(
        userId,
        selectedFile,
        (progress) => {
          setUploadProgress(progress);
        }
      );

      setCompressionStats(result.compressionStats);
      setSuccess(true);
      onUploadComplete?.(result.avatarUrls);

      // Clean up
      setTimeout(() => {
        setSelectedFile(null);
        if (previewUrl) {
          cleanupImagePreview(previewUrl);
          setPreviewUrl(null);
        }
        setSuccess(false);
        setUploadProgress(null);
      }, 3000);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Greška pri upload-u';
      setError(errorMessage);
      onUploadError?.(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancel = () => {
    setSelectedFile(null);
    setError(null);
    setSuccess(false);
    setUploadProgress(null);
    
    if (previewUrl) {
      cleanupImagePreview(previewUrl);
      setPreviewUrl(null);
    }
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Current Avatar Display */}
      {currentAvatarUrl && !selectedFile && (
        <div className="flex items-center space-x-4">
          <div className="relative w-20 h-20 rounded-full overflow-hidden bg-gray-100">
            <Image
              src={currentAvatarUrl}
              alt="Current avatar"
              fill
              className="object-cover"
            />
          </div>
          <div>
            <p className="text-sm font-medium">Trenutna profilna slika</p>
            <p className="text-xs text-muted-foreground">
              Kliknite ili povucite novu sliku da je zamijenite
            </p>
          </div>
        </div>
      )}

      {/* Upload Area */}
      <Card className={`transition-colors ${isDragging ? 'border-primary bg-primary/5' : ''}`}>
        <CardContent className="p-6">
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragging 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-muted-foreground/50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {selectedFile ? (
              // Preview selected file
              <div className="space-y-4">
                <div className="relative w-32 h-32 mx-auto rounded-full overflow-hidden bg-gray-100">
                  {previewUrl && (
                    <Image
                      src={previewUrl}
                      alt="Preview"
                      fill
                      className="object-cover"
                    />
                  )}
                </div>
                <div>
                  <p className="font-medium">{selectedFile.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(selectedFile.size)}
                  </p>
                </div>
                <div className="flex gap-2 justify-center">
                  <Button onClick={handleUpload} disabled={isUploading}>
                    {isUploading ? 'Upload u toku...' : 'Upload sliku'}
                  </Button>
                  <Button variant="outline" onClick={handleCancel} disabled={isUploading}>
                    Otkaži
                  </Button>
                </div>
              </div>
            ) : (
              // Upload prompt
              <div className="space-y-4">
                <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                  <Camera className="w-8 h-8 text-muted-foreground" />
                </div>
                <div>
                  <p className="text-lg font-medium">Dodajte profilnu sliku</p>
                  <p className="text-sm text-muted-foreground">
                    Povucite sliku ovdje ili kliknite da izaberete
                  </p>
                  <p className="text-xs text-muted-foreground mt-2">
                    Podržani formati: JPEG, PNG, WebP (max 10MB)
                  </p>
                </div>
                <Button onClick={openFileDialog} variant="outline">
                  <Upload className="w-4 h-4 mr-2" />
                  Izaberite sliku
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Progress */}
      {uploadProgress && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  {uploadProgress.message || 'Upload u toku...'}
                </span>
                <span className="text-sm text-muted-foreground">
                  {uploadProgress.percentage}%
                </span>
              </div>
              <Progress value={uploadProgress.percentage} className="w-full" />
              {uploadProgress.currentSize && (
                <p className="text-xs text-muted-foreground">
                  Trenutno: {uploadProgress.currentSize} verzija
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Success message */}
      {success && compressionStats && (
        <Alert>
          <Check className="h-4 w-4" />
          <AlertDescription>
            Slika je uspješno upload-ovana! Kompresija: {compressionStats.compressionRatio}% 
            (sa {formatFileSize(compressionStats.originalSize)} na {formatFileSize(compressionStats.totalCompressedSize)})
          </AlertDescription>
        </Alert>
      )}

      {/* Error message */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
