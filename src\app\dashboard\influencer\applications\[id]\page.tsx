'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  ArrowLeft,
  Calendar,
  FileText,
  Link as LinkIcon,
  Clock,
  CheckCircle,
  Send,
  Loader2,
  Hash,
  Ban,
  Tag,
} from 'lucide-react';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { getCampaignApplication } from '@/lib/campaigns';
import { getJobCompletionByCampaignApplication } from '@/lib/job-completions';
import { CampaignJobSubmissionForm } from '@/components/job-completion/CampaignJobSubmissionForm';
import { ChatE<PERSON>bleButton } from '@/components/chat/ChatEnableButton';
import { formatDate } from '@/lib/date-utils';
import { ApplicationWithDetails } from '@/lib/types';
import { JobCompletionWithDetails } from '@/lib/job-completions';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';

export default function InfluencerApplicationDetailsPage() {
  const params = useParams();
  const [application, setApplication] = useState<ApplicationWithDetails | null>(
    null
  );
  const [jobCompletion, setJobCompletion] =
    useState<JobCompletionWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLoadingJobCompletion, setIsLoadingJobCompletion] = useState(false);
  const [showJobSubmission, setShowJobSubmission] = useState(false);

  const loadApplication = async () => {
    try {
      const { data, error } = await getCampaignApplication(params.id as string);
      if (error) {
        console.error('Error loading application:', error);
        return;
      }
      setApplication(data);
    } catch (error) {
      console.error('Error loading application:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadJobCompletion = async (applicationId: string) => {
    setIsLoadingJobCompletion(true);
    try {
      const { data } =
        await getJobCompletionByCampaignApplication(applicationId);
      setJobCompletion(data);
    } catch (error) {
      console.error('Error loading job completion:', error);
    } finally {
      setIsLoadingJobCompletion(false);
    }
  };

  useEffect(() => {
    loadApplication();
  }, [params.id]);

  useEffect(() => {
    if (application?.status === 'accepted') {
      loadJobCompletion(application.id);
    }
  }, [application]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const getJobStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Čeka se početak rada</Badge>;
      case 'submitted':
        return <Badge variant="outline">Poslano na pregled</Badge>;
      case 'approved':
        return (
          <Badge variant="default" className="bg-green-600">
            Odobreno
          </Badge>
        );
      case 'rejected':
        return <Badge variant="destructive">Odbačeno</Badge>;
      case 'completed':
        return (
          <Badge variant="default" className="bg-blue-600">
            Završeno
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  if (!application) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold mb-2">Aplikacija nije pronađena</h2>
          <p className="text-muted-foreground">
            Možda je obrisana ili nemate dozvolu da je vidite.
          </p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          {/* Desktop: Back button */}
          <div className="hidden md:block">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/influencer/applications">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Nazad
              </Link>
            </Button>
          </div>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold">
              Detalji aplikacije
            </h1>
            <p className="text-muted-foreground">
              Pregled vaše aplikacije za kampanju
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Application Status */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Status aplikacije</CardTitle>
                  <Badge className={getStatusColor(application.status)}>
                    {getStatusText(application.status)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      Aplicirali:{' '}
                      {formatDate(application.applied_at)}
                    </span>
                  </div>

                  {application.status === 'rejected' &&
                    application.rejection_reason && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-800">
                          <strong>Razlog odbacivanja:</strong>{' '}
                          {application.rejection_reason}
                        </p>
                      </div>
                    )}
                </div>
              </CardContent>
            </Card>

            {/* Campaign Details */}
            <Card>
              <CardHeader>
                <CardTitle>Detalji kampanje</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium text-lg">
                    {application.campaign.title}
                  </h3>
                  <p className="text-muted-foreground mt-1">
                    {application.campaign.description}
                  </p>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-2">Budget</h4>
                  <div className="flex items-center gap-2">
                    <span>{application.campaign.budget} €</span>
                  </div>
                </div>

              </CardContent>
            </Card>

            {/* Platforms and Content Types */}
            {application.campaign.platforms && application.campaign.platforms.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Platforme i tipovi sadržaja</CardTitle>
                  <CardDescription>
                    Gde influencer treba da objavi sadržaj i koji tip sadržaja
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {application.campaign.platforms.map((platform: any, index: number) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <span className="text-lg">{platform.icon}</span>
                          <span className="font-medium">{platform.name}</span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {platform.content_types?.map((type: string, typeIndex: number) => (
                            <Badge key={typeIndex} variant="secondary">
                              {type === 'post' ? 'Photo post' :
                               type === 'video' ? 'Video' :
                               type === 'story' ? 'Story' :
                               type === 'reel' ? 'Shorts' : type}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Hashtags */}
            {application.campaign.hashtags && application.campaign.hashtags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Hash className="h-5 w-5" />
                    Obavezni hashtag-ovi
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {application.campaign.hashtags.map((hashtag: string, index: number) => (
                      <Badge key={index} variant="outline" className="font-mono">
                        #{hashtag}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Do Not Mention */}
            {application.campaign.do_not_mention && application.campaign.do_not_mention.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Ban className="h-5 w-5" />
                    Ne spominjati
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {application.campaign.do_not_mention.map((item: string, index: number) => (
                      <Badge key={index} variant="destructive">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Additional Notes */}
            {application.campaign.additional_notes && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Dodatne napomene
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                    {application.campaign.additional_notes}
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Job Completion Section */}
            {application.status === 'accepted' && (
              <Card>
                <CardHeader>
                  <CardTitle>Završetak posla</CardTitle>
                  <CardDescription>
                    Označite posao kao završen kada završite sve dogovorene
                    aktivnosti
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingJobCompletion ? (
                    <div className="animate-pulse space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ) : !jobCompletion && !showJobSubmission ? (
                    <div className="text-center py-6">
                      <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">
                        Spremni za submission?
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        Kada završite sve dogovorene aktivnosti, možete označiti
                        posao kao završen.
                      </p>
                      <Button onClick={() => setShowJobSubmission(true)}>
                        <Send className="mr-2 h-4 w-4" />
                        Označi kao završeno
                      </Button>
                    </div>
                  ) : showJobSubmission && !jobCompletion ? (
                    <CampaignJobSubmissionForm
                      campaignApplicationId={application.id}
                      campaignTitle={application.campaign.title}
                      businessName={application.campaign.business?.company_name}
                      businessAvatar={
                        application.campaign.business?.profiles?.avatar_url
                      }
                      proposedRate={application.proposed_rate}
                      onSuccess={() => {
                        setShowJobSubmission(false);
                        loadJobCompletion(application.id);
                      }}
                      onCancel={() => setShowJobSubmission(false)}
                    />
                  ) : jobCompletion ? (
                    <div className="space-y-4">
                      <div className="text-center py-6">
                        <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          Završetak posla je poslan!
                        </h3>
                        <p className="text-muted-foreground mb-4">
                          {jobCompletion.status === 'submitted' &&
                            'Vaš rad je poslan biznisu na pregled. Čekamo njihovu potvrdu.'}
                          {jobCompletion.status === 'approved' &&
                            'Biznis je odobrio vaš rad. Čestitamo!'}
                          {jobCompletion.status === 'rejected' &&
                            'Biznis je odbacio vaš rad. Molimo kontaktirajte ih za više informacija.'}
                        </p>
                        <Badge
                          variant={
                            jobCompletion.status === 'approved'
                              ? 'default'
                              : jobCompletion.status === 'rejected'
                                ? 'destructive'
                                : 'secondary'
                          }
                          className="mb-4"
                        >
                          {jobCompletion.status === 'approved'
                            ? 'Odobreno'
                            : jobCompletion.status === 'rejected'
                              ? 'Odbijeno'
                              : jobCompletion.status === 'submitted'
                                ? 'Na čekanju pregleda'
                                : 'Pending'}
                        </Badge>
                      </div>

                      {jobCompletion.submitted_at && (
                        <div className="text-center">
                          <p className="text-sm text-muted-foreground">
                            Poslano{' '}
                            {formatDistanceToNow(
                              new Date(jobCompletion.submitted_at),
                              { addSuffix: true, locale: hr }
                            )}
                          </p>
                        </div>
                      )}

                      {jobCompletion.submission_notes && (
                        <div>
                          <h4 className="font-medium mb-2">Vaše napomene:</h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                            {jobCompletion.submission_notes}
                          </p>
                        </div>
                      )}

                      {jobCompletion.business_notes && (
                        <div>
                          <h4 className="font-medium mb-2">
                            Napomene biznisa:
                          </h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                            {jobCompletion.business_notes}
                          </p>
                        </div>
                      )}
                    </div>
                  ) : null}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Your Application */}
            <Card>
              <CardHeader>
                <CardTitle>Vaša aplikacija</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <span className="text-sm font-medium">
                    Predložena cijena:
                  </span>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="font-medium">
                      {application.proposed_rate} €
                    </span>
                  </div>
                </div>

                <div>
                  <span className="text-sm font-medium">Vremenski okvir:</span>
                  <p className="text-sm text-gray-600 mt-1">
                    {application.delivery_timeframe}
                  </p>
                </div>

                <div>
                  <span className="text-sm font-medium">Vaš prijedlog:</span>
                  <p className="text-sm text-gray-600 mt-1">
                    {application.proposal_text}
                  </p>
                </div>


                {application.experience_relevant && (
                  <div>
                    <span className="text-sm font-medium">Relevantno iskustvo:</span>
                    <p className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">
                      {application.experience_relevant}
                    </p>
                  </div>
                )}

                {application.audience_insights && (
                  <div>
                    <span className="text-sm font-medium">Informacije o publici:</span>
                    <p className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">
                      {application.audience_insights}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Business Info */}
            <Card>
              <CardHeader>
                <CardTitle>Biznis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage
                      src={
                        application.campaign.business?.profiles?.avatar_url ||
                        ''
                      }
                    />
                    <AvatarFallback>
                      {application.campaign.business?.company_name?.charAt(0) ||
                        'B'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-medium">
                      {application.campaign.business?.company_name}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      @{application.campaign.business?.profiles?.username}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Chat */}
            {application.status === 'accepted' &&
              application.campaign.business_id && (
                <Card>
                  <CardHeader>
                    <CardTitle>Komunikacija</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ChatEnableButton
                      businessId={application.campaign.business_id}
                      influencerId={application.influencer_id}
                      applicationId={application.id}
                    />
                  </CardContent>
                </Card>
              )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
