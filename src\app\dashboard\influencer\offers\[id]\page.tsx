'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  Euro,
  MessageCircle,
  Building2,
  Mail,
  Phone,
  MapPin,
  Send,
} from 'lucide-react';
import {
  getDirectOffer,
  updateOfferStatus,
  type DirectOfferWithDetails,
} from '@/lib/offers';
import { formatDistanceToNow } from 'date-fns';
import { hr } from 'date-fns/locale';
import { toast } from 'sonner';
import Link from 'next/link';
import { ChatEnableButton } from '@/components/chat/ChatEnableButton';
import { DirectOfferJobSubmissionForm } from '@/components/job-completion/DirectOfferJobSubmissionForm';
import {
  getUserJobCompletions,
  getJobCompletionByDirectOffer,
  type JobCompletion,
} from '@/lib/job-completions';
import { formatDate } from '@/lib/date-utils';

export default function InfluencerOfferDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [offer, setOffer] = useState<DirectOfferWithDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [response, setResponse] = useState('');
  const [jobCompletion, setJobCompletion] = useState<any>(null);
  const [showJobSubmission, setShowJobSubmission] = useState(false);

  useEffect(() => {
    if (params.id) {
      loadOffer(params.id as string);
      loadJobCompletion();
    }
  }, [params.id]);

  const loadOffer = async (offerId: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await getDirectOffer(offerId);
      if (error) {
        console.error('Error loading offer:', error);
        router.push('/dashboard/influencer/offers');
      } else {
        setOffer(data);
      }
    } catch (error) {
      console.error('Error loading offer:', error);
      router.push('/dashboard/influencer/offers');
    } finally {
      setIsLoading(false);
    }
  };

  const loadJobCompletion = async () => {
    try {
      const { data: jobCompletion, error } =
        await getJobCompletionByDirectOffer(params.id as string);
      if (error) {
        console.error('Error loading job completion:', error);
        setJobCompletion(null);
      } else {
        setJobCompletion(jobCompletion);
      }
    } catch (error) {
      console.error('Error loading job completion:', error);
      setJobCompletion(null);
    }
  };

  const handleOfferResponse = async (status: 'accepted' | 'rejected') => {
    if (!offer || !response.trim()) {
      toast.error('Molimo unesite odgovor prije prihvatanja/odbijanja ponude');
      return;
    }

    setIsUpdating(true);
    try {
      const { error } = await updateOfferStatus(offer.id, status, response);
      if (error) {
        toast.error('Greška pri ažuriranju ponude');
      } else {
        toast.success(
          status === 'accepted' ? 'Ponuda je prihvaćena!' : 'Ponuda je odbijena'
        );
        await loadOffer(offer.id);
      }
    } catch (error) {
      console.error('Error updating offer:', error);
      toast.error('Greška pri ažuriranju ponude');
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge
            variant="outline"
            className="text-yellow-600 border-yellow-600"
          >
            <Clock className="w-3 h-3 mr-1" />
            Na čekanju
          </Badge>
        );
      case 'accepted':
        return (
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Prihvaćeno
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="text-red-600 border-red-600">
            <XCircle className="w-3 h-3 mr-1" />
            Odbijeno
          </Badge>
        );
      case 'completed':
        return (
          <Badge variant="outline" className="text-blue-600 border-blue-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Završeno
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="text-gray-600 border-gray-600">
            <XCircle className="w-3 h-3 mr-1" />
            Otkazano
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (isLoading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!offer) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Ponuda nije pronađena</h2>
          <Button asChild>
            <Link href="/dashboard/influencer/offers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Nazad na ponude
            </Link>
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          {/* Desktop: Back button */}
          <div className="hidden md:block">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/influencer/offers">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Nazad
              </Link>
            </Button>
          </div>
          <div className="flex-1">
            <h1 className="text-2xl md:text-3xl font-bold">{offer.title}</h1>
            <p className="text-muted-foreground mt-1">
              Ponuda primljena{' '}
              {formatDistanceToNow(new Date(offer.created_at), {
                addSuffix: true,
                locale: hr,
              })}
            </p>
          </div>
          {getStatusBadge(offer.status)}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Offer Details */}
            <Card>
              <CardHeader>
                <CardTitle>Detalji ponude</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {offer.offer_type === 'custom' && (
                  <div>
                    <h4 className="font-medium mb-2">Opis projekta</h4>
                    <p className="text-muted-foreground">{offer.description}</p>
                  </div>
                )}

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Cijena</h4>
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-semibold">
                        {offer.budget.toLocaleString()} €
                      </span>
                    </div>
                  </div>
                  {offer.deadline && (
                    <div>
                      <h4 className="font-medium mb-2">Rok za završetak</h4>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {formatDate(offer.deadline)}
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium mb-2">Tipovi sadržaja</h4>
                  <div className="flex flex-wrap gap-2">
                    {offer.content_types.map((type, index) => (
                      <Badge key={index} variant="secondary">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Platforme</h4>
                  <div className="flex flex-wrap gap-2">
                    {offer.platforms.map((platform, index) => (
                      <Badge key={index} variant="outline">
                        {platform}
                      </Badge>
                    ))}
                  </div>
                </div>

                {offer.requirements &&
                 (offer.offer_type === 'custom' ||
                  (offer.offer_type === 'package_order' && !offer.requirements.startsWith('Automatski kreirana narudžba'))) && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium mb-2">Specifični zahtjevi</h4>
                      <p className="text-muted-foreground">
                        {offer.requirements}
                      </p>
                    </div>
                  </>
                )}

                {offer.deliverables && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium mb-2">Očekivani rezultati</h4>
                      <p className="text-muted-foreground">
                        {offer.deliverables}
                      </p>
                    </div>
                  </>
                )}

                <Separator />

                {offer.offer_type === 'package_order' && offer.description &&
                 !offer.description.startsWith('Narudžba paketa') && (
                  <div>
                    <h4 className="font-medium mb-2">Poruka od biznisa</h4>
                    <p className="text-muted-foreground">
                      {offer.description}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Response Section */}
            {offer.status === 'pending' && (
              <Card>
                <CardHeader>
                  <CardTitle>Vaš odgovor</CardTitle>
                  <CardDescription>
                    Molimo odgovorite na ponudu i prihvatite ili odbijte je
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="Unesite vaš odgovor na ponudu..."
                    value={response}
                    onChange={e => setResponse(e.target.value)}
                    rows={4}
                  />
                  <div className="flex gap-3">
                    <Button
                      onClick={() => handleOfferResponse('accepted')}
                      disabled={isUpdating || !response.trim()}
                      className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Prihvati ponudu
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleOfferResponse('rejected')}
                      disabled={isUpdating || !response.trim()}
                      className="flex-1"
                    >
                      <XCircle className="mr-2 h-4 w-4" />
                      Odbij ponudu
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Response Status */}
            {offer.status !== 'pending' && offer.responded_at && (
              <Card>
                <CardHeader>
                  <CardTitle>Status odgovora</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-sm text-muted-foreground">
                    {offer.status === 'accepted' && (
                      <p>
                        Ponuda prihvaćena{' '}
                        {formatDistanceToNow(new Date(offer.responded_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                    {offer.status === 'rejected' && (
                      <p>
                        Ponuda odbijena{' '}
                        {formatDistanceToNow(new Date(offer.responded_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Job Completion Section */}
            {offer.status === 'accepted' && (
              <Card>
                <CardHeader>
                  <CardTitle>Završetak posla</CardTitle>
                  <CardDescription>
                    Označite posao kao završen kada završite sve dogovorene
                    aktivnosti
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {!jobCompletion && !showJobSubmission && (
                    <div className="text-center py-6">
                      <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">
                        Spremni za submission?
                      </h3>
                      <p className="text-muted-foreground mb-4">
                        Kada završite sve dogovorene aktivnosti, možete označiti
                        posao kao završen.
                      </p>
                      <Button onClick={() => setShowJobSubmission(true)}>
                        <Send className="mr-2 h-4 w-4" />
                        Označi kao završeno
                      </Button>
                    </div>
                  )}

                  {showJobSubmission && !jobCompletion && (
                    <DirectOfferJobSubmissionForm
                      directOfferId={params.id as string}
                      offerTitle={offer.title}
                      businessName={offer.businesses.company_name}
                      businessAvatar={offer.businesses.profiles.avatar_url}
                      proposedRate={offer.budget}
                      onSuccess={() => {
                        setShowJobSubmission(false);
                        loadJobCompletion();
                      }}
                      onCancel={() => setShowJobSubmission(false)}
                    />
                  )}

                  {jobCompletion && (
                    <div className="space-y-4">
                      <div className="text-center py-6">
                        <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          Završetak posla je poslan!
                        </h3>
                        <p className="text-muted-foreground mb-4">
                          {jobCompletion.status === 'submitted' &&
                            'Vaš rad je poslan biznisu na pregled. Čekamo njihovu potvrdu.'}
                          {jobCompletion.status === 'approved' &&
                            'Biznis je odobrio vaš rad. Čestitamo!'}
                          {jobCompletion.status === 'rejected' &&
                            'Biznis je odbacio vaš rad. Molimo kontaktirajte ih za više informacija.'}
                        </p>
                        <Badge
                          variant={
                            jobCompletion.status === 'approved'
                              ? 'default'
                              : jobCompletion.status === 'rejected'
                                ? 'destructive'
                                : 'secondary'
                          }
                          className="mb-4"
                        >
                          {jobCompletion.status === 'approved'
                            ? 'Odobreno'
                            : jobCompletion.status === 'rejected'
                              ? 'Odbijeno'
                              : jobCompletion.status === 'submitted'
                                ? 'Na čekanju pregleda'
                                : 'Pending'}
                        </Badge>
                      </div>

                      {jobCompletion.submitted_at && (
                        <div className="text-center">
                          <p className="text-sm text-muted-foreground">
                            Poslano{' '}
                            {formatDistanceToNow(
                              new Date(jobCompletion.submitted_at),
                              { addSuffix: true, locale: hr }
                            )}
                          </p>
                        </div>
                      )}

                      {jobCompletion.submission_notes && (
                        <div>
                          <h4 className="font-medium mb-2">Vaše napomene:</h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                            {jobCompletion.submission_notes}
                          </p>
                        </div>
                      )}

                      {jobCompletion.business_notes && (
                        <div>
                          <h4 className="font-medium mb-2">
                            Napomene biznisa:
                          </h4>
                          <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                            {jobCompletion.business_notes}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Business Info */}
            <Card>
              <CardHeader>
                <CardTitle>Biznis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-3 mb-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage
                      src={offer.businesses.profiles.avatar_url || ''}
                      alt={offer.businesses.company_name}
                    />
                    <AvatarFallback className="text-lg">
                      <Building2 className="h-6 w-6" />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-lg">
                      {offer.businesses.company_name}
                    </h3>
                    {offer.businesses.industry && (
                      <p className="text-muted-foreground">
                        {offer.businesses.industry}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  {/* Chat button will be shown in ChatPermissionStatus component */}
                </div>
              </CardContent>
            </Card>

            {/* Chat Enable Button */}
            {offer.status === 'accepted' && (
              <Card>
                <CardHeader>
                  <CardTitle>Komunikacija</CardTitle>
                </CardHeader>
                <CardContent>
                  <ChatEnableButton
                    offerId={offer.id}
                    businessId={offer.business_id}
                    influencerId={offer.influencer_id}
                    onChatEnabled={() => {
                      console.log('Chat enabled for offer:', offer.id);
                    }}
                  />
                </CardContent>
              </Card>
            )}

            {/* Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>Istorija</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium">Ponuda primljena</p>
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(offer.created_at), {
                          addSuffix: true,
                          locale: hr,
                        })}
                      </p>
                    </div>
                  </div>

                  {offer.status === 'accepted' && offer.responded_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium">Ponuda prihvaćena</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(offer.responded_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}

                  {offer.status === 'rejected' && offer.responded_at && (
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium">Ponuda odbijena</p>
                        <p className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(offer.responded_at), {
                            addSuffix: true,
                            locale: hr,
                          })}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
