'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { TabsContent } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
} from 'lucide-react';
import Link from 'next/link';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { supabase } from '@/lib/supabase';
import { formatDate } from '@/lib/date-utils';

interface Application {
  id: string;
  campaign_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_rate: string;
  proposal_text: string;
  delivery_timeframe: string;
  portfolio_links: string[] | null;
  experience_relevant: string | null;
  audience_insights: string | null;
  applied_at: string;
  campaigns: {
    id: string;
    title: string;
    budget: number;
    business_id: string;
  };
}

export default function InfluencerApplicationsPage() {
  const { user } = useAuth();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      fetchApplications();
    }
  }, [user?.id]);

  const fetchApplications = async () => {
    try {
      const { data, error } = await supabase
        .from('campaign_applications')
        .select(`
          *,
          campaigns (
            id,
            title,
            budget,
            business_id
          )
        `)
        .eq('influencer_id', user?.id)
        .order('applied_at', { ascending: false });

      if (error) throw error;
      setApplications(data || []);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const pendingCount = applications.filter(app => app.status === 'pending').length;
  const acceptedCount = applications.filter(app => app.status === 'accepted').length;
  const rejectedCount = applications.filter(app => app.status === 'rejected').length;

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Učitavanje...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">Moje aplikacije</h1>
          <p className="text-muted-foreground mt-1">
            Pregledajte status vaših aplikacija na kampanje
          </p>
        </div>

        {/* Applications List */}
        <TabsWithBadge
          defaultValue="all"
          className="space-y-6"
          tabs={[
            { name: "Sve", value: "all", count: applications.length },
            { name: "Na čekanju", value: "pending", count: pendingCount },
            { name: "Prihvaćeno", value: "accepted", count: acceptedCount },
            { name: "Odbačeno", value: "rejected", count: rejectedCount }
          ]}
        >
          <TabsContent value="all" className="space-y-4">
            {applications.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">Nemate aplikacija još uvijek.</p>
              </div>
            ) : (
              applications.map((application) => (
                <ApplicationCard key={application.id} application={application} />
              ))
            )}
          </TabsContent>

          <TabsContent value="pending" className="space-y-4">
            {applications.filter(app => app.status === 'pending').map((application) => (
              <ApplicationCard key={application.id} application={application} />
            ))}
          </TabsContent>

          <TabsContent value="accepted" className="space-y-4">
            {applications.filter(app => app.status === 'accepted').map((application) => (
              <ApplicationCard key={application.id} application={application} />
            ))}
          </TabsContent>

          <TabsContent value="rejected" className="space-y-4">
            {applications.filter(app => app.status === 'rejected').map((application) => (
              <ApplicationCard key={application.id} application={application} />
            ))}
          </TabsContent>
        </TabsWithBadge>
      </div>
    </DashboardLayout>
  );
}

function ApplicationCard({ application }: { application: Application }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'accepted':
        return <CheckCircle className="h-4 w-4" />;
      case 'rejected':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header with title and status */}
          <div className="flex items-start justify-between">
            <h3 className="text-lg font-semibold text-gray-900 flex-1 pr-4">
              {application.campaigns.title}
            </h3>

            {/* Status in top right corner */}
            <Badge className={`flex items-center space-x-1 ${getStatusColor(application.status)}`}>
              {getStatusIcon(application.status)}
              <span>{getStatusText(application.status)}</span>
            </Badge>
          </div>

          {/* Details */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <span>{application.proposed_rate} €</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{application.delivery_timeframe}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(application.applied_at)}</span>
            </div>
          </div>

          {/* Action button at the bottom */}
          <div className="flex items-center pt-2 border-t">
            <Link href={`/dashboard/influencer/applications/${application.id}`} className="w-full">
              <Button variant="outline" size="sm" className="w-full">
                <Eye className="h-4 w-4 mr-2" />
                Pregled
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
