import { supabase } from './supabase';
import { 
  compressImageWithProgress, 
  type CompressionProgressCallback,
  type ImageSizeType 
} from './image-compression';

// Storage bucket configuration
const AVATAR_BUCKET = 'avatars';

// Storage folder structure - organized by user ID first for RLS compatibility
const getStoragePath = (userId: string, sizeType: ImageSizeType): string => {
  return `${userId}/${sizeType}`;
};

export interface AvatarUrls {
  navbar_url: string;
  card_url: string;
  profile_url: string;
  preview_url: string;
}

export interface UploadProgress {
  stage: 'validating' | 'compressing' | 'uploading' | 'updating_profile' | 'complete';
  percentage: number;
  currentSize?: ImageSizeType;
  message?: string;
}

/**
 * Upload avatar with compression and multiple sizes
 */
export async function uploadAvatarWithCompression(
  userId: string,
  file: File,
  onProgress?: (progress: UploadProgress) => void
): Promise<{
  avatarUrls: AvatarUrls;
  compressionStats: {
    originalSize: number;
    totalCompressedSize: number;
    compressionRatio: number;
  };
}> {
  try {
    // Step 1: Compress images
    onProgress?.({ 
      stage: 'compressing', 
      percentage: 0, 
      message: 'Kompresovanje slika...' 
    });

    const compressionResult = await compressImageWithProgress(
      file,
      (progress) => {
        onProgress?.({
          stage: progress.stage,
          percentage: Math.round(progress.percentage * 0.4), // 40% of total progress
          currentSize: progress.currentSize,
          message: progress.stage === 'compressing' 
            ? `Kompresovanje ${progress.currentSize} verzije...`
            : 'Validacija slike...'
        });
      }
    );

    // Step 2: Upload all sizes to storage
    onProgress?.({ 
      stage: 'uploading', 
      percentage: 40, 
      message: 'Upload slika na server...' 
    });

    const uploadPromises = Object.entries(compressionResult).map(
      async ([sizeType, compressedFile], index) => {
        if (sizeType === 'originalSize' || sizeType === 'totalCompressedSize' || sizeType === 'compressionRatio') {
          return null;
        }

        const size = sizeType as ImageSizeType;
        const fileName = `${Date.now()}-${size}.webp`;
        const filePath = `${getStoragePath(userId, size)}/${fileName}`;

        const { error: uploadError } = await supabase.storage
          .from(AVATAR_BUCKET)
          .upload(filePath, compressedFile as File, {
            cacheControl: '3600',
            upsert: true,
          });

        if (uploadError) {
          throw new Error(`Upload failed for ${size}: ${uploadError.message}`);
        }

        // Get public URL
        const { data } = supabase.storage
          .from(AVATAR_BUCKET)
          .getPublicUrl(filePath);

        onProgress?.({
          stage: 'uploading',
          percentage: 40 + ((index + 1) * 10), // 40-80%
          currentSize: size,
          message: `Upload ${size} verzije završen...`
        });

        return { size, url: data.publicUrl };
      }
    );

    const uploadResults = await Promise.all(uploadPromises);
    const validResults = uploadResults.filter(result => result !== null) as Array<{
      size: ImageSizeType;
      url: string;
    }>;

    // Create avatar URLs object
    const avatarUrls: AvatarUrls = {
      navbar_url: validResults.find(r => r.size === 'navbar')?.url || '',
      card_url: validResults.find(r => r.size === 'card')?.url || '',
      profile_url: validResults.find(r => r.size === 'profile')?.url || '',
      preview_url: validResults.find(r => r.size === 'preview')?.url || '',
    };

    // Step 3: Update profile in database
    onProgress?.({ 
      stage: 'updating_profile', 
      percentage: 80, 
      message: 'Ažuriranje profila...' 
    });

    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        avatar_url: avatarUrls.profile_url, // Keep backward compatibility
        navbar_avatar_url: avatarUrls.navbar_url,
        card_avatar_url: avatarUrls.card_url,
        profile_avatar_url: avatarUrls.profile_url,
        preview_avatar_url: avatarUrls.preview_url,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId);

    if (updateError) {
      throw new Error(`Profile update failed: ${updateError.message}`);
    }

    onProgress?.({ 
      stage: 'complete', 
      percentage: 100, 
      message: 'Upload završen!' 
    });

    return {
      avatarUrls,
      compressionStats: {
        originalSize: compressionResult.originalSize,
        totalCompressedSize: compressionResult.totalCompressedSize,
        compressionRatio: compressionResult.compressionRatio,
      },
    };

  } catch (error) {
    console.error('Avatar upload error:', error);
    throw error;
  }
}

/**
 * Delete old avatar files from storage
 */
export async function deleteOldAvatarFiles(userId: string): Promise<void> {
  try {
    // List all files for this user in their folder
    const { data: files, error } = await supabase.storage
      .from(AVATAR_BUCKET)
      .list(userId, {
        limit: 100,
      });

    if (error) {
      console.error(`Error listing files for user ${userId}:`, error);
      return;
    }

    if (files && files.length > 0) {
      // Get all subfolders (navbar, card, profile, preview)
      const subfolders = files.filter(item => item.name && !item.name.includes('.'));

      for (const subfolder of subfolders) {
        const { data: subFiles, error: subError } = await supabase.storage
          .from(AVATAR_BUCKET)
          .list(`${userId}/${subfolder.name}`);

        if (subError) {
          console.error(`Error listing files in ${userId}/${subfolder.name}:`, subError);
          continue;
        }

        if (subFiles && subFiles.length > 0) {
          const filePaths = subFiles.map(file => `${userId}/${subfolder.name}/${file.name}`);

          const { error: deleteError } = await supabase.storage
            .from(AVATAR_BUCKET)
            .remove(filePaths);

          if (deleteError) {
            console.error(`Error deleting files in ${userId}/${subfolder.name}:`, deleteError);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error deleting old avatar files:', error);
    // Don't throw error here as it's not critical
  }
}

/**
 * Get avatar URLs for a user
 */
export async function getAvatarUrls(userId: string): Promise<AvatarUrls | null> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        navbar_avatar_url,
        card_avatar_url,
        profile_avatar_url,
        preview_avatar_url,
        avatar_url
      `)
      .eq('id', userId)
      .single();

    if (error || !data) {
      return null;
    }

    // Fallback to main avatar_url if specific sizes don't exist
    return {
      navbar_url: data.navbar_avatar_url || data.avatar_url || '',
      card_url: data.card_avatar_url || data.avatar_url || '',
      profile_url: data.profile_avatar_url || data.avatar_url || '',
      preview_url: data.preview_avatar_url || data.avatar_url || '',
    };
  } catch (error) {
    console.error('Error getting avatar URLs:', error);
    return null;
  }
}

/**
 * Check if storage bucket exists and create if needed
 */
export async function ensureAvatarBucket(): Promise<void> {
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.error('Error listing buckets:', error);
      return;
    }

    const avatarBucket = buckets?.find(bucket => bucket.name === AVATAR_BUCKET);
    
    if (!avatarBucket) {
      const { error: createError } = await supabase.storage.createBucket(AVATAR_BUCKET, {
        public: true,
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/webp'],
        fileSizeLimit: 10485760, // 10MB
      });

      if (createError) {
        console.error('Error creating avatar bucket:', createError);
      }
    }
  } catch (error) {
    console.error('Error ensuring avatar bucket:', error);
  }
}
