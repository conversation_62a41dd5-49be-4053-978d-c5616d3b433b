-- CURRENCY MIGRATION: KM to EUR
-- Migracija svih valuta sa KM na EUR kroz cijelu aplikaciju
-- Pokrenuti u Supabase SQL Editor

-- 1. <PERSON>reiranje backup tabela prije migracije (za <PERSON>gurno<PERSON>)
CREATE TABLE IF NOT EXISTS backup_influencer_platform_pricing AS 
SELECT * FROM influencer_platform_pricing;

CREATE TABLE IF NOT EXISTS backup_campaigns AS 
SELECT * FROM campaigns;

CREATE TABLE IF NOT EXISTS backup_direct_offers AS 
SELECT * FROM direct_offers;

CREATE TABLE IF NOT EXISTS backup_collaborations AS 
SELECT * FROM collaborations;

CREATE TABLE IF NOT EXISTS backup_influencers AS 
SELECT * FROM influencers;

-- 2. <PERSON><PERSON><PERSON><PERSON><PERSON> funkcije za konverziju KM u EUR
CREATE OR REPLACE FUNCTION convert_km_to_eur(km_amount DECIMAL)
RETURNS DECIMAL AS $$
BEGIN
  -- 1 KM ≈ 0.51 EUR (Bosnia and Herzegovina convertible mark to Euro)
  IF km_amount IS NULL THEN
    RETURN NULL;
  END IF;
  
  -- <PERSON>n<PERSON><PERSON><PERSON> i zaokruži na 2 decimale
  RETURN ROUND(km_amount * 0.51, 2);
END;
$$ LANGUAGE plpgsql;

-- 3. Ažuriranje influencer_platform_pricing tabele
-- Prvo konvertuj postojeće cijene iz KM u EUR
UPDATE influencer_platform_pricing 
SET 
  price = convert_km_to_eur(price),
  currency = 'EUR'
WHERE currency = 'KM' OR currency IS NULL;

-- Promijeni default vrijednost za currency kolonu
ALTER TABLE influencer_platform_pricing 
ALTER COLUMN currency SET DEFAULT 'EUR';

-- 4. Ažuriranje campaigns tabele
-- Konvertuj budžete iz KM u EUR
UPDATE campaigns 
SET budget = convert_km_to_eur(budget)
WHERE budget IS NOT NULL;

-- 5. Ažuriranje direct_offers tabele
-- Konvertuj budžete iz KM u EUR
UPDATE direct_offers 
SET budget = convert_km_to_eur(budget)
WHERE budget IS NOT NULL;

-- 6. Ažuriranje collaborations tabele
-- Konvertuj dogovorene cijene iz KM u EUR
UPDATE collaborations 
SET agreed_price = convert_km_to_eur(agreed_price)
WHERE agreed_price IS NOT NULL;

-- 7. Ažuriranje influencers tabele
-- Konvertuj cijene po objavi/story/reel iz KM u EUR
UPDATE influencers 
SET 
  price_per_post = convert_km_to_eur(price_per_post),
  price_per_story = convert_km_to_eur(price_per_story),
  price_per_reel = convert_km_to_eur(price_per_reel)
WHERE 
  price_per_post IS NOT NULL 
  OR price_per_story IS NOT NULL 
  OR price_per_reel IS NOT NULL;

-- 8. Ažuriranje businesses tabele - budget_range
-- Konvertuj budget range stringove
UPDATE businesses 
SET budget_range = CASE 
  WHEN budget_range = '0-500 KM' THEN '0-255 EUR'
  WHEN budget_range = '500-1000 KM' THEN '255-510 EUR'
  WHEN budget_range = '1000-2000 KM' THEN '510-1020 EUR'
  WHEN budget_range = '2000-5000 KM' THEN '1020-2550 EUR'
  WHEN budget_range = '5000+ KM' THEN '2550+ EUR'
  ELSE budget_range
END
WHERE budget_range IS NOT NULL AND budget_range LIKE '%KM%';

-- 9. Ažuriranje materialized view-a da koriste EUR
-- Refresh influencer_search_view nakon promjena
DROP MATERIALIZED VIEW IF EXISTS influencer_search_view;

-- Kreiranje novog materialized view-a sa EUR podrškom
CREATE MATERIALIZED VIEW influencer_search_view AS
SELECT 
  i.id,
  p.username,
  p.full_name,
  p.avatar_url,
  p.bio,
  p.location,
  i.gender,
  i.age,
  i.is_verified,
  p.average_rating,
  p.total_reviews,
  
  -- Kategorije kao array
  COALESCE(
    ARRAY_AGG(DISTINCT c.name) FILTER (WHERE c.name IS NOT NULL),
    '{}'::varchar[]
  ) as categories,
  
  -- Platforme kao JSON
  COALESCE(
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'platform_id', plat.id,
        'platform_name', plat.name,
        'platform_icon', plat.icon,
        'handle', ip.handle,
        'followers_count', ip.followers_count,
        'is_verified', ip.is_verified
      ) ORDER BY plat.name
    ) FILTER (WHERE plat.id IS NOT NULL AND ip.is_active = true),
    '[]'::json
  ) as platforms,

  -- Cijene kao JSON (sada u EUR)
  COALESCE(
    JSON_AGG(
      JSON_BUILD_OBJECT(
        'platform_id', pricing_plat.id,
        'platform_name', pricing_plat.name,
        'content_type_id', ct.id,
        'content_type_name', ct.name,
        'price', ipp.price,
        'currency', ipp.currency
      ) ORDER BY pricing_plat.name, ct.name
    ) FILTER (WHERE ipp.id IS NOT NULL AND ipp.is_available = true),
    '[]'::json
  ) as pricing,

  -- Min/Max cijene u EUR
  COALESCE(MIN(ipp.price) FILTER (WHERE ipp.is_available = true), 0) as min_price,
  COALESCE(MAX(ipp.price) FILTER (WHERE ipp.is_available = true), 0) as max_price,

  -- Ukupni broj pratilaca
  COALESCE(SUM(ip.followers_count) FILTER (WHERE ip.is_active = true), 0) as total_followers,

  -- Search vector za full-text pretragu
  setweight(to_tsvector('simple', COALESCE(p.full_name, '')), 'A') ||
  setweight(to_tsvector('simple', COALESCE(p.username, '')), 'A') ||
  setweight(to_tsvector('simple', COALESCE(p.bio, '')), 'B') ||
  setweight(to_tsvector('simple', COALESCE(p.location, '')), 'C') ||
  setweight(to_tsvector('simple', COALESCE(string_agg(DISTINCT c.name, ' '), '')), 'B') as search_vector,

  i.created_at,
  i.updated_at

FROM influencers i
JOIN profiles p ON i.id = p.id
LEFT JOIN influencer_categories ic ON i.id = ic.influencer_id
LEFT JOIN categories c ON ic.category_id = c.id
LEFT JOIN influencer_platforms ip ON i.id = ip.influencer_id
LEFT JOIN platforms plat ON ip.platform_id = plat.id
LEFT JOIN influencer_platform_pricing ipp ON i.id = ipp.influencer_id
LEFT JOIN platforms pricing_plat ON ipp.platform_id = pricing_plat.id
LEFT JOIN content_types ct ON ipp.content_type_id = ct.id
WHERE p.user_type = 'influencer'
GROUP BY 
  i.id, p.username, p.full_name, p.avatar_url, p.bio, p.location,
  i.gender, i.age, i.is_verified, p.average_rating, p.total_reviews,
  i.created_at, i.updated_at;

-- Kreiranje indeksa za optimizaciju
CREATE INDEX IF NOT EXISTS idx_influencer_search_view_search_vector 
ON influencer_search_view USING gin(search_vector);

CREATE INDEX IF NOT EXISTS idx_influencer_search_view_min_price 
ON influencer_search_view(min_price);

CREATE INDEX IF NOT EXISTS idx_influencer_search_view_max_price 
ON influencer_search_view(max_price);

-- 10. Kreiranje funkcije za refresh materialized view
CREATE OR REPLACE FUNCTION refresh_influencer_search_view()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY influencer_search_view;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Inicijalno refresh materialized view
SELECT refresh_influencer_search_view();

-- 12. Kreiranje funkcije za validaciju EUR iznosa
CREATE OR REPLACE FUNCTION validate_eur_amount(amount DECIMAL)
RETURNS BOOLEAN AS $$
BEGIN
  -- Provjeri da li je iznos valjan EUR iznos
  RETURN amount IS NOT NULL AND amount >= 0 AND amount <= 100000;
END;
$$ LANGUAGE plpgsql;

-- 13. Dodavanje check constraint-a za EUR validaciju
ALTER TABLE influencer_platform_pricing 
DROP CONSTRAINT IF EXISTS check_valid_eur_price;

ALTER TABLE influencer_platform_pricing 
ADD CONSTRAINT check_valid_eur_price 
CHECK (validate_eur_amount(price));

-- 14. Kreiranje funkcije za formatiranje EUR iznosa
CREATE OR REPLACE FUNCTION format_eur_amount(amount DECIMAL)
RETURNS TEXT AS $$
BEGIN
  IF amount IS NULL THEN
    RETURN '0 €';
  END IF;
  
  RETURN amount::TEXT || ' €';
END;
$$ LANGUAGE plpgsql;

-- 15. Kreiranje view-a za lakše dohvaćanje formatiranih cijena
CREATE OR REPLACE VIEW formatted_pricing_view AS
SELECT 
  ipp.*,
  format_eur_amount(ipp.price) as formatted_price,
  p.name as platform_name,
  ct.name as content_type_name
FROM influencer_platform_pricing ipp
JOIN platforms p ON ipp.platform_id = p.id
JOIN content_types ct ON ipp.content_type_id = ct.id
WHERE ipp.is_available = true;

-- 16. Kreiranje trigger-a za automatsko postavljanje EUR valute
CREATE OR REPLACE FUNCTION set_default_eur_currency()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.currency IS NULL OR NEW.currency = '' THEN
    NEW.currency := 'EUR';
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_set_default_eur_currency ON influencer_platform_pricing;
CREATE TRIGGER trigger_set_default_eur_currency
  BEFORE INSERT OR UPDATE ON influencer_platform_pricing
  FOR EACH ROW
  EXECUTE FUNCTION set_default_eur_currency();

-- 17. Čišćenje - uklanjanje backup tabela (opcionalno, pokrenuti nakon testiranja)
-- DROP TABLE IF EXISTS backup_influencer_platform_pricing;
-- DROP TABLE IF EXISTS backup_campaigns;
-- DROP TABLE IF EXISTS backup_direct_offers;
-- DROP TABLE IF EXISTS backup_collaborations;
-- DROP TABLE IF EXISTS backup_influencers;

-- 18. Kreiranje log zapisa o migraciji
INSERT INTO public.migration_logs (migration_name, executed_at, description)
VALUES (
  'currency_migration_to_eur',
  NOW(),
  'Migrated all currency values from KM to EUR with conversion rate 1 KM = 0.51 EUR'
) ON CONFLICT DO NOTHING;

-- Kreiranje tabele za migration logs ako ne postoji
CREATE TABLE IF NOT EXISTS migration_logs (
  id SERIAL PRIMARY KEY,
  migration_name VARCHAR(255) UNIQUE NOT NULL,
  executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  description TEXT
);

COMMENT ON TABLE migration_logs IS 'Log of executed database migrations';
COMMENT ON COLUMN migration_logs.migration_name IS 'Unique name of the migration';
COMMENT ON COLUMN migration_logs.executed_at IS 'When the migration was executed';
COMMENT ON COLUMN migration_logs.description IS 'Description of what the migration does';
