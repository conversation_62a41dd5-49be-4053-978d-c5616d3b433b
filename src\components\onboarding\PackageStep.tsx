'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, X, Loader2, Package } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  getPlatforms,
  getContentTypes,
  generatePackageName,
  VIDEO_DURATIONS,
  type Platform,
  type ContentType,
} from '@/lib/pricing-packages';

const packageSchema = z.object({
  platform_id: z.number().min(1, 'Izaberite platformu'),
  content_type_id: z.number().min(1, 'Izaberite tip sadržaja'),
  quantity: z.number().min(1, 'Količina mora biti najmanje 1'),
  video_duration: z.string().optional().or(z.literal('')),
  price: z.number().min(0.01, 'Cijena mora biti veća od 0'),
});

type PackageForm = z.infer<typeof packageSchema>;

interface PackageData {
  platform_id: number;
  content_type_id: number;
  quantity: number;
  video_duration?: string;
  price: number;
  // For display
  platform_name: string;
  platform_icon: string;
  content_type_name: string;
  generated_name: string;
}

interface PackageStepProps {
  packages: PackageData[];
  onUpdate: (packages: PackageData[]) => void;
  onFinish: () => void;
  onBack: () => void;
  isLoading: boolean;
}

export function PackageStep({ packages, onUpdate, onFinish, onBack, isLoading }: PackageStepProps) {
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [contentTypes, setContentTypes] = useState<ContentType[]>([]);
  const [isAddingPackage, setIsAddingPackage] = useState(false);
  const [loadingData, setLoadingData] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<PackageForm>({
    resolver: zodResolver(packageSchema),
  });

  const watchedPlatformId = watch('platform_id');
  const watchedContentTypeId = watch('content_type_id');
  const watchedQuantity = watch('quantity');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [platformsResult, contentTypesResult] = await Promise.all([
        getPlatforms(),
        getContentTypes(),
      ]);

      if (platformsResult.error) {
        throw new Error('Failed to load platforms: ' + platformsResult.error.message);
      }

      if (contentTypesResult.error) {
        throw new Error('Failed to load content types: ' + contentTypesResult.error.message);
      }

      setPlatforms(platformsResult.data || []);
      setContentTypes(contentTypesResult.data || []);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoadingData(false);
    }
  };

  const getAvailableContentTypes = () => {
    if (!watchedPlatformId) return [];
    return contentTypes.filter(ct => ct.platform_id === watchedPlatformId);
  };

  const getSelectedContentType = () => {
    return contentTypes.find(ct => ct.id === watchedContentTypeId);
  };

  const shouldShowVideoDuration = () => {
    const contentType = getSelectedContentType();
    return contentType?.requires_video_duration || false;
  };

  const handleAddPackage = (data: PackageForm) => {
    const platform = platforms.find(p => p.id === data.platform_id);
    const contentType = contentTypes.find(ct => ct.id === data.content_type_id);

    if (!platform || !contentType) return;

    const generatedName = generatePackageName(
      data.quantity,
      platform.name,
      contentType.name,
      data.video_duration
    );

    const newPackage: PackageData = {
      platform_id: data.platform_id,
      content_type_id: data.content_type_id,
      quantity: data.quantity,
      video_duration: data.video_duration || undefined,
      price: data.price,
      platform_name: platform.name,
      platform_icon: platform.icon,
      content_type_name: contentType.name,
      generated_name: generatedName,
    };

    onUpdate([...packages, newPackage]);
    setIsAddingPackage(false);
    reset();
  };

  const handleRemovePackage = (index: number) => {
    const updatedPackages = packages.filter((_, i) => i !== index);
    onUpdate(updatedPackages);
  };

  const handleCancelAdd = () => {
    setIsAddingPackage(false);
    reset();
  };

  if (loadingData) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Kreirajte pakete usluga
        </h2>
        <p className="text-gray-600">
          Dodajte pakete koje nudite brendovima (opcionalno)
        </p>
      </div>

      {/* Existing packages */}
      {packages.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Krerani paketi</h3>
          <div className="space-y-3">
            {packages.map((pkg, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                        <Package className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium">{pkg.generated_name}</p>
                        <p className="text-sm text-gray-600">
                          {pkg.platform_name} • {pkg.price} €
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemovePackage(index)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Add package form */}
      {isAddingPackage && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>Dodaj novi paket</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(handleAddPackage)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="platform_id">Platforma</Label>
                  <Select
                    value={watchedPlatformId?.toString() || ''}
                    onValueChange={(value) => {
                      setValue('platform_id', parseInt(value));
                      setValue('content_type_id', 0); // Reset content type
                    }}
                  >
                    <SelectTrigger className={errors.platform_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Izaberite platformu" />
                    </SelectTrigger>
                    <SelectContent>
                      {platforms.map((platform) => (
                        <SelectItem key={platform.id} value={platform.id.toString()}>
                          <div className="flex items-center space-x-2">
                            <span>{platform.icon}</span>
                            <span>{platform.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.platform_id && (
                    <p className="text-sm text-red-500">{errors.platform_id.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="content_type_id">Tip sadržaja</Label>
                  <Select
                    value={watchedContentTypeId?.toString() || ''}
                    onValueChange={(value) => setValue('content_type_id', parseInt(value))}
                    disabled={!watchedPlatformId}
                  >
                    <SelectTrigger className={errors.content_type_id ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Izaberite tip sadržaja" />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableContentTypes().map((contentType) => (
                        <SelectItem key={contentType.id} value={contentType.id.toString()}>
                          {contentType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.content_type_id && (
                    <p className="text-sm text-red-500">{errors.content_type_id.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quantity">Količina</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    placeholder="1"
                    {...register('quantity', { valueAsNumber: true })}
                    className={errors.quantity ? 'border-red-500' : ''}
                  />
                  {errors.quantity && (
                    <p className="text-sm text-red-500">{errors.quantity.message}</p>
                  )}
                </div>

                {shouldShowVideoDuration() && (
                  <div className="space-y-2">
                    <Label htmlFor="video_duration">Trajanje videa</Label>
                    <Select
                      value={watch('video_duration') || ''}
                      onValueChange={(value) => setValue('video_duration', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Izaberite trajanje" />
                      </SelectTrigger>
                      <SelectContent>
                        {VIDEO_DURATIONS.map((duration) => (
                          <SelectItem key={duration.value} value={duration.value}>
                            {duration.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="price">Cijena (KM)</Label>
                  <Input
                    id="price"
                    type="number"
                    min="0.01"
                    step="0.01"
                    placeholder="50.00"
                    {...register('price', { valueAsNumber: true })}
                    className={errors.price ? 'border-red-500' : ''}
                  />
                  {errors.price && (
                    <p className="text-sm text-red-500">{errors.price.message}</p>
                  )}
                </div>
              </div>

              <div className="flex space-x-2">
                <Button type="submit">Dodaj paket</Button>
                <Button type="button" variant="outline" onClick={handleCancelAdd}>
                  Otkaži
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Add package button */}
      {!isAddingPackage && (
        <div className="space-y-4">
          <Button
            onClick={() => setIsAddingPackage(true)}
            variant="outline"
            className="w-full"
          >
            <Plus className="h-4 w-4 mr-2" />
            Dodaj paket
          </Button>
        </div>
      )}

      {/* Navigation */}
      {!isAddingPackage && (
        <div className="space-y-4">
          {packages.length === 0 && (
            <div className="text-center py-4">
              <p className="text-gray-600 mb-4">
                Možete preskočiti ovaj korak i dodati pakete kasnije.
              </p>
            </div>
          )}
          
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onBack}
              className="flex-1"
              disabled={isLoading}
            >
              Nazad
            </Button>
            <Button 
              onClick={onFinish} 
              className="flex-1"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Završavam...
                </>
              ) : (
                'Završi'
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
