'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TabsWithBadge } from '@/components/ui/tabs-with-badge';
import { TabsContent } from '@/components/ui/tabs';
import {
  Plus,
  Edit,
  Eye,
  Calendar,
  Users,
  Loader2,
  CheckCircle,
  Clock,
  AlertCircle,
} from 'lucide-react';
import Link from 'next/link';

import { getBusinessCampaigns, updateCampaignStatus } from '@/lib/campaigns';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { formatDate } from '@/lib/date-utils';

interface Campaign {
  id: string;
  title: string;
  description: string;
  budget: number | null;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  created_at: string;
  application_count?: number;
  content_types: string[];
}

export default function BusinessCampaignsPage() {
  console.log('BusinessCampaignsPage component rendered');

  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [activatingCampaign, setActivatingCampaign] = useState<string | null>(
    null
  );

  console.log('BusinessCampaignsPage - current state:', {
    user: user?.id,
    userObject: user,
    authLoading,
    campaignsCount: campaigns.length,
    loading,
  });

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/prijava');
      return;
    }

    if (user) {
      loadCampaigns();
    }
  }, [user, authLoading, router]);

  const loadCampaigns = async () => {
    try {
      setLoading(true);

      if (!user?.id) {
        return;
      }

      const result = await getBusinessCampaigns(user.id);
      setCampaigns(result?.data || []);
    } catch (error) {
      console.error('Error loading campaigns:', error);
    } finally {
      setLoading(false);
    }
  };

  const activateCampaign = async (campaignId: string) => {
    try {
      setActivatingCampaign(campaignId);
      console.log('Activating campaign:', campaignId, 'for user:', user?.id);

      const { error } = await updateCampaignStatus(campaignId, 'active');

      if (error) {
        console.error('Error activating campaign:', error);
        return;
      }

      console.log('Campaign activated successfully');
      // Refresh campaigns
      await loadCampaigns();
    } catch (error) {
      console.error('Error activating campaign:', error);
    } finally {
      setActivatingCampaign(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Clock className="h-4 w-4" />;
      case 'active':
        return <CheckCircle className="h-4 w-4" />;
      case 'paused':
        return <AlertCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<
      string,
      'default' | 'secondary' | 'destructive' | 'outline'
    > = {
      draft: 'outline',
      active: 'default',
      paused: 'secondary',
      completed: 'secondary',
    };

    const labels: Record<string, string> = {
      draft: 'Neaktivna',
      active: 'Aktivna',
      paused: 'Pauzirana',
      completed: 'Završena',
    };

    return (
      <Badge
        variant={variants[status] || 'outline'}
        className="flex items-center gap-1"
      >
        {getStatusIcon(status)}
        {labels[status] || status}
      </Badge>
    );
  };

  const draftCampaigns = campaigns.filter(c => c.status === 'draft');
  const activeCampaigns = campaigns.filter(c => c.status === 'active');
  const otherCampaigns = campaigns.filter(
    c => !['draft', 'active'].includes(c.status)
  );

  if (authLoading || loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading campaigns...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Moje Kampanje</h1>
            <p className="text-muted-foreground mt-1">
              Upravljajte svojim kampanjama i pratite performanse
            </p>
          </div>

          <Link href="/campaigns/create">
            <Button className="w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              Nova Kampanja
            </Button>
          </Link>
        </div>



        {/* Campaigns Tabs */}
        <TabsWithBadge
          defaultValue="draft"
          className="space-y-6"
          tabs={[
            { name: "Neaktivne", value: "draft", count: draftCampaigns.length },
            { name: "Aktivne", value: "active", count: activeCampaigns.length },
            { name: "Pauzirane", value: "other", count: otherCampaigns.length }
          ]}
        >

          <TabsContent value="draft" className="space-y-4">
            {draftCampaigns.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <p className="text-muted-foreground mb-4">
                    Nemate draft kampanja
                  </p>
                  <Link href="/campaigns/create">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Kreiraj prvu kampanju
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ) : (
              draftCampaigns.map(campaign => (
                <CampaignCard
                  key={campaign.id}
                  campaign={campaign}
                  onActivate={activateCampaign}
                  isActivating={activatingCampaign === campaign.id}
                />
              ))
            )}
          </TabsContent>

          <TabsContent value="active" className="space-y-4">
            {activeCampaigns.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <p className="text-muted-foreground">
                    Nemate aktivnih kampanja
                  </p>
                </CardContent>
              </Card>
            ) : (
              activeCampaigns.map(campaign => (
                <CampaignCard
                  key={campaign.id}
                  campaign={campaign}
                  onActivate={activateCampaign}
                  isActivating={false}
                />
              ))
            )}
          </TabsContent>

          <TabsContent value="other" className="space-y-4">
            {otherCampaigns.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <p className="text-muted-foreground">
                    Nemate drugih kampanja
                  </p>
                </CardContent>
              </Card>
            ) : (
              otherCampaigns.map(campaign => (
                <CampaignCard
                  key={campaign.id}
                  campaign={campaign}
                  onActivate={activateCampaign}
                  isActivating={false}
                />
              ))
            )}
          </TabsContent>
        </TabsWithBadge>
      </div>
    </DashboardLayout>
  );
}

interface CampaignCardProps {
  campaign: Campaign;
  onActivate: (id: string) => void;
  isActivating: boolean;
}

function CampaignCard({
  campaign,
  onActivate,
  isActivating,
}: CampaignCardProps) {
  const getStatusBadge = (status: string) => {
    const variants: Record<
      string,
      'default' | 'secondary' | 'destructive' | 'outline'
    > = {
      draft: 'outline',
      active: 'default',
      paused: 'secondary',
      completed: 'secondary',
    };

    const labels: Record<string, string> = {
      draft: 'Neaktivna',
      active: 'Aktivna',
      paused: 'Pauzirana',
      completed: 'Završena',
    };

    return (
      <Badge variant={variants[status] || 'outline'}>
        {labels[status] || status}
      </Badge>
    );
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header with title and status */}
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold flex-1">{campaign.title}</h3>
            {getStatusBadge(campaign.status)}
          </div>

          {/* Description */}
          <p className="text-muted-foreground line-clamp-2">
            {campaign.description}
          </p>

          {/* Campaign details */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              {campaign.budget?.toLocaleString() || 'N/A'} €
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {formatDate(campaign.created_at)}
            </div>
            {campaign.content_types && (
              <div>
                {campaign.content_types.slice(0, 2).join(', ')}
                {campaign.content_types.length > 2 &&
                  ` +${campaign.content_types.length - 2}`}
              </div>
            )}
          </div>

          {/* Action buttons at the bottom */}
          <div className="flex flex-wrap items-center gap-2 pt-2 border-t">
            <Link href={`/campaigns/${campaign.id}`}>
              <Button variant="outline" size="sm">
                <Eye className="h-4 w-4 mr-1" />
                Pregled
              </Button>
            </Link>

            {campaign.status === 'draft' && (
              <>
                <Link href={`/campaigns/${campaign.id}/edit`}>
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4 mr-1" />
                    Uredi
                  </Button>
                </Link>

                <Button
                  size="sm"
                  onClick={() => onActivate(campaign.id)}
                  disabled={isActivating}
                >
                  {isActivating && (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  )}
                  Aktiviraj
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
