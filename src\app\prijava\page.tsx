'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  GradientBackground,
  GradientCard,
  GradientButton,
  GradientInput,
  GradientHeader
} from '@/components/ui';
import {
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Eye, EyeOff, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const loginSchema = z.object({
  email: z.string().email('Unesite validnu email adresu'),
  password: z.string().min(1, 'Unesite lozinku'),
  rememberMe: z.boolean().optional(),
});

type LoginForm = z.infer<typeof loginSchema>;

export default function PrijavaPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signIn } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get redirect URL from search params
  const redirectTo = searchParams.get('redirect') || '/dashboard';

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginForm) => {
    setIsLoading(true);

    try {
      const { data: authData, error } = await signIn(data.email, data.password);

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          setError('root', { message: 'Neispravni podaci za prijavu' });
        } else if (error.message.includes('Email not confirmed')) {
          setError('root', {
            message: 'Molimo potvrdite svoj email prije prijave',
          });
        } else {
          setError('root', { message: error.message });
        }
        return;
      }

      if (authData.user) {
        // Check if user has a profile, if not redirect to profile creation
        // Otherwise redirect to the intended page or dashboard
        if (
          redirectTo === '/dashboard' ||
          redirectTo.startsWith('/dashboard')
        ) {
          router.push('/dashboard'); // This will handle profile check and redirect appropriately
        } else {
          router.push(redirectTo);
        }
      }
    } catch (error) {
      setError('root', { message: 'Došlo je do greške. Pokušajte ponovo.' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <GradientBackground variant="secondary" decorativeElements={true}>
      {/* Header */}
      <GradientHeader
        backHref="/"
        backText="Nazad"
        title="INFLUEXUS"
      />

      {/* Main Content */}
      <main className="relative z-10 flex-1 flex items-center justify-center px-4 py-12">
        <GradientCard variant="secondary" glassEffect={true} className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-white">Prijava</CardTitle>
            <CardDescription className="text-white/70">
              Unesite svoje podatke da biste se prijavili
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              {/* Email */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-white">Email adresa</Label>
                <GradientInput
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  error={!!errors.email}
                />
                {errors.email && (
                  <p className="text-sm text-red-300">{errors.email.message}</p>
                )}
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-white">Lozinka</Label>
                <div className="relative">
                  <GradientInput
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Unesite lozinku"
                    {...register('password')}
                    error={!!errors.password}
                  />
                  <GradientButton
                    type="button"
                    gradientVariant="glass"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 text-white/60 hover:text-white"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </GradientButton>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-300">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <input
                    id="rememberMe"
                    type="checkbox"
                    {...register('rememberMe')}
                    className="rounded border-white/30 bg-white/10 text-instagram-purple focus:ring-white/20"
                  />
                  <Label htmlFor="rememberMe" className="text-sm text-white">
                    Zapamti me
                  </Label>
                </div>
                <Link
                  href="/zaboravljena-lozinka"
                  className="text-sm text-white/80 hover:text-white hover:underline transition-colors"
                >
                  Zaboravili ste lozinku?
                </Link>
              </div>

              {/* Submit Button */}
              <GradientButton
                type="submit"
                gradientVariant="white"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Prijavljivanje...
                  </>
                ) : (
                  'Prijavite se'
                )}
              </GradientButton>

              {/* Root Error */}
              {errors.root && (
                <p className="text-sm text-red-300 text-center bg-red-500/20 p-3 rounded-lg border border-red-400/30">
                  {errors.root.message}
                </p>
              )}
            </form>


            {/* Registration Link */}
            <div className="mt-6 text-center">
              <p className="text-sm text-white/70">
                Nemate nalog?{' '}
                <Link
                  href="/registracija"
                  className="text-white hover:underline font-medium transition-colors"
                >
                  Registrujte se
                </Link>
              </p>
            </div>
          </CardContent>
        </GradientCard>
      </main>
    </GradientBackground>
  );
}
