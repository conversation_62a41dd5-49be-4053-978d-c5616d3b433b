import { supabase } from './supabase';
import {
  isChatEnabled,
  upsertApplicationChatPermission,
  upsertOfferChatPermission,
} from './chat-permissions';

export interface ChatRoom {
  id: string;
  business_id: string;
  influencer_id: string;
  campaign_application_id: string | null;
  offer_id: string | null;
  room_title: string;
  room_type: string;
  created_at: string | null;
  updated_at: string | null;
  last_message_at: string | null;

  // Joined data
  business_profile?: {
    full_name: string;
    public_display_name: string | null;
    username: string;
    avatar_url: string | null;
  };
  influencer_profile?: {
    full_name: string;
    public_display_name: string | null;
    username: string;
    avatar_url: string | null;
  };
  unread_count?: number;
}

export interface ChatMessage {
  id: string;
  room_id: string;
  sender_id: string;
  sender_type: string;
  message_text: string | null;
  file_url: string | null;
  file_name: string | null;
  file_type: string | null;
  file_size: number | null;
  created_at: string | null;
  read_at: string | null;
  edited_at: string | null;

  // Joined data
  sender_profile?: {
    full_name: string;
    public_display_name: string | null;
    username: string;
    avatar_url: string | null;
  };
}

export interface ChatParticipant {
  id: string;
  room_id: string;
  user_id: string;
  user_type: 'business' | 'influencer';
  joined_at: string;
  last_read_at: string | null;
  is_active: boolean;
}

/**
 * Get or create a chat room for a campaign application
 */
export async function getOrCreateChatRoomForApplication(
  campaignApplicationId: string
): Promise<{ data: ChatRoom | null; error: any }> {
  try {
    // First, get the campaign application details
    const { data: application, error: appError } = await supabase
      .from('campaign_applications')
      .select(
        `
        id,
        campaign_id,
        influencer_id,
        campaigns!inner(
          id,
          title,
          business_id
        )
      `
      )
      .eq('id', campaignApplicationId)
      .single();

    if (appError || !application) {
      return { data: null, error: appError || 'Application not found' };
    }

    const businessId = application.campaigns.business_id;
    const influencerId = application.influencer_id;
    const campaignTitle = application.campaigns.title;

    // Check if chat is enabled via permissions
    const chatEnabled = await isChatEnabled(
      businessId,
      influencerId,
      undefined,
      campaignApplicationId
    );
    if (!chatEnabled) {
      return { data: null, error: 'Chat not enabled for this application' };
    }

    // Check if room already exists
    const { data: existingRoom, error: roomError } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('business_id', businessId)
      .eq('influencer_id', influencerId)
      .eq('campaign_application_id', campaignApplicationId)
      .single();

    if (existingRoom) {
      return { data: existingRoom, error: null };
    }

    // Create new room
    const roomTitle = `Kampanja: ${campaignTitle}`;
    const { data: newRoom, error: createError } = await supabase
      .from('chat_rooms')
      .insert({
        business_id: businessId,
        influencer_id: influencerId,
        campaign_application_id: campaignApplicationId,
        room_title: roomTitle,
        room_type: 'campaign_application',
      })
      .select()
      .single();

    return { data: newRoom, error: createError };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get or create a chat room for a direct offer
 */
export async function getOrCreateChatRoomForOffer(
  offerId: string
): Promise<{ data: ChatRoom | null; error: any }> {
  try {
    // First, get the offer details
    const { data: offer, error: offerError } = await supabase
      .from('direct_offers')
      .select('id, title, business_id, influencer_id')
      .eq('id', offerId)
      .single();

    if (offerError || !offer) {
      return { data: null, error: offerError || 'Offer not found' };
    }

    // Check if chat is enabled via permissions
    const chatEnabled = await isChatEnabled(
      offer.business_id,
      offer.influencer_id,
      offerId
    );
    if (!chatEnabled) {
      return { data: null, error: 'Chat not enabled for this offer' };
    }

    // Check if room already exists
    const { data: existingRoom, error: roomError } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('business_id', offer.business_id)
      .eq('influencer_id', offer.influencer_id)
      .eq('offer_id', offerId)
      .single();

    if (existingRoom) {
      return { data: existingRoom, error: null };
    }

    // Create new room
    const roomTitle = `Direktna ponuda: ${offer.title}`;
    const { data: newRoom, error: createError } = await supabase
      .from('chat_rooms')
      .insert({
        business_id: offer.business_id,
        influencer_id: offer.influencer_id,
        offer_id: offerId,
        room_title: roomTitle,
        room_type: 'direct_offer',
      })
      .select()
      .single();

    return { data: newRoom, error: createError };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get all chat rooms for current user
 */
export async function getUserChatRooms(): Promise<{
  data: ChatRoom[] | null;
  error: any;
}> {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      return { data: null, error: 'Not authenticated' };
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', user.user.id)
      .single();

    if (!profile) {
      return { data: null, error: 'Profile not found' };
    }

    // Get rooms based on user type - simplified query first
    const query = supabase
      .from('chat_rooms')
      .select('*')
      .order('last_message_at', { ascending: false, nullsFirst: false });

    if (profile.user_type === 'business') {
      query.eq('business_id', user.user.id);
    } else {
      query.eq('influencer_id', user.user.id);
    }

    const { data: rooms, error } = await query;

    if (error) {
      return { data: null, error };
    }

    // Enhance rooms with profile data
    if (rooms && rooms.length > 0) {
      const enhancedRooms = await Promise.all(
        rooms.map(async room => {
          // Get business profile
          const { data: businessProfile } = await supabase
            .from('profiles')
            .select('full_name, public_display_name, username, avatar_url')
            .eq('id', room.business_id)
            .single();

          // Get influencer profile
          const { data: influencerProfile } = await supabase
            .from('profiles')
            .select('full_name, public_display_name, username, avatar_url')
            .eq('id', room.influencer_id)
            .single();

          return {
            ...room,
            business_profile: businessProfile,
            influencer_profile: influencerProfile,
          };
        })
      );

      return { data: enhancedRooms, error: null };
    }

    return { data: rooms || [], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Get messages for a chat room
 */
export async function getChatMessages(
  roomId: string,
  limit: number = 50,
  offset: number = 0
): Promise<{ data: ChatMessage[] | null; error: any }> {
  try {
    const { data: messages, error } = await supabase
      .from('chat_messages')
      .select(
        `
        *,
        sender_profile:profiles!chat_messages_sender_id_fkey(
          full_name,
          public_display_name,
          username,
          avatar_url
        )
      `
      )
      .eq('room_id', roomId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      return { data: null, error };
    }

    // Reverse to show oldest first
    return { data: messages?.reverse() || [], error: null };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Send a message to a chat room
 */
export async function sendChatMessage(
  roomId: string,
  messageText?: string,
  fileUrl?: string,
  fileName?: string,
  fileType?: string,
  fileSize?: number
): Promise<{ data: ChatMessage | null; error: any }> {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      return { data: null, error: 'Not authenticated' };
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('user_type')
      .eq('id', user.user.id)
      .single();

    if (!profile) {
      return { data: null, error: 'Profile not found' };
    }

    // Validate that either message text or file is provided
    if (!messageText && !fileUrl) {
      return { data: null, error: 'Message must contain text or file' };
    }

    const { data: message, error } = await supabase
      .from('chat_messages')
      .insert({
        room_id: roomId,
        sender_id: user.user.id,
        sender_type: profile.user_type,
        message_text: messageText,
        file_url: fileUrl,
        file_name: fileName,
        file_type: fileType,
        file_size: fileSize,
      })
      .select(
        `
        *,
        sender_profile:profiles!chat_messages_sender_id_fkey(
          full_name,
          username,
          avatar_url
        )
      `
      )
      .single();

    return { data: message, error };
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Mark messages as read
 */
export async function markMessagesAsRead(
  roomId: string,
  messageIds?: string[]
): Promise<{ error: any }> {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      return { error: 'Not authenticated' };
    }

    let query = supabase
      .from('chat_messages')
      .update({ read_at: new Date().toISOString() })
      .eq('room_id', roomId)
      .neq('sender_id', user.user.id)
      .is('read_at', null);

    if (messageIds) {
      // Ensure messageIds is an array
      const idsArray = Array.isArray(messageIds) ? messageIds : [messageIds];

      // Validate that all messageIds are valid UUIDs
      const validIds = idsArray.filter(
        id =>
          typeof id === 'string' &&
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(
            id
          )
      );

      if (validIds.length > 0) {
        query = query.in('id', validIds);
      }
    }

    const { error } = await query;
    return { error };
  } catch (error) {
    return { error };
  }
}

/**
 * Update participant's last read timestamp
 */
export async function updateLastReadAt(
  roomId: string
): Promise<{ error: any }> {
  try {
    const { data: user } = await supabase.auth.getUser();
    if (!user.user) {
      return { error: 'Not authenticated' };
    }

    const { error } = await supabase
      .from('chat_participants')
      .update({ last_read_at: new Date().toISOString() })
      .eq('room_id', roomId)
      .eq('user_id', user.user.id);

    return { error };
  } catch (error) {
    return { error };
  }
}

/**
 * Enable chat for a campaign application (creates permission and room)
 */
export async function enableChatForApplication(
  campaignApplicationId: string,
  businessApproved: boolean = true,
  influencerApproved: boolean = true
): Promise<{ data: ChatRoom | null; error: any }> {
  try {
    // Get application details
    const { data: application, error: appError } = await supabase
      .from('campaign_applications')
      .select(
        `
        id,
        campaign_id,
        influencer_id,
        campaigns!inner(
          id,
          title,
          business_id
        )
      `
      )
      .eq('id', campaignApplicationId)
      .single();

    if (appError || !application) {
      return { data: null, error: appError || 'Application not found' };
    }

    const businessId = application.campaigns.business_id;
    const influencerId = application.influencer_id;

    // Create or update chat permission
    await upsertApplicationChatPermission(
      businessId,
      influencerId,
      campaignApplicationId,
      businessApproved,
      influencerApproved
    );

    // Create chat room
    return await getOrCreateChatRoomForApplication(campaignApplicationId);
  } catch (error) {
    return { data: null, error };
  }
}

/**
 * Enable chat for a direct offer (creates permission and room)
 */
export async function enableChatForOffer(
  offerId: string,
  businessApproved: boolean = true,
  influencerApproved: boolean = true
): Promise<{ data: ChatRoom | null; error: any }> {
  try {
    console.log('enableChatForOffer: Starting for offer:', offerId);
    // Get offer details
    const { data: offer, error: offerError } = await supabase
      .from('direct_offers')
      .select('id, title, business_id, influencer_id')
      .eq('id', offerId)
      .single();

    if (offerError || !offer) {
      console.log('enableChatForOffer: Offer error:', offerError);
      return { data: null, error: offerError || 'Offer not found' };
    }

    console.log('enableChatForOffer: Offer found:', offer);

    // Create or update chat permission
    console.log('enableChatForOffer: Creating permission...');
    await upsertOfferChatPermission(
      offer.business_id,
      offer.influencer_id,
      offerId,
      businessApproved,
      influencerApproved
    );

    // Create chat room
    console.log('enableChatForOffer: Creating chat room...');
    const result = await getOrCreateChatRoomForOffer(offerId);
    console.log('enableChatForOffer: Result:', result);
    return result;
  } catch (error) {
    console.log('enableChatForOffer: Catch error:', error);
    return { data: null, error };
  }
}

/**
 * Get a specific chat room by ID
 */
export async function getChatRoom(
  roomId: string
): Promise<{ data: ChatRoom | null; error: any }> {
  try {
    console.log('getChatRoom: Loading room:', roomId);

    // First get the basic room data
    const { data: room, error: roomError } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('id', roomId)
      .single();

    console.log('getChatRoom: Room data:', { room, roomError });

    if (roomError || !room) {
      return { data: null, error: roomError };
    }

    // Then get the profile data separately
    const { data: businessProfile } = await supabase
      .from('profiles')
      .select('full_name, public_display_name, username, avatar_url')
      .eq('id', room.business_id)
      .single();

    const { data: influencerProfile } = await supabase
      .from('profiles')
      .select('full_name, public_display_name, username, avatar_url')
      .eq('id', room.influencer_id)
      .single();

    console.log('getChatRoom: Profiles:', {
      businessProfile,
      influencerProfile,
    });

    // Combine the data
    const enrichedRoom = {
      ...room,
      business_profile: businessProfile,
      influencer_profile: influencerProfile,
    };

    console.log('getChatRoom: Final room:', enrichedRoom);
    return { data: enrichedRoom, error: null };
  } catch (error) {
    console.log('getChatRoom: Catch error:', error);
    return { data: null, error };
  }
}
