import React from 'react';
import { cn } from '@/lib/utils';

interface GradientBackgroundProps {
  variant?: 'primary' | 'secondary' | 'story' | 'subtle' | 'warm' | 'sunset' | 'ocean' | 'forest' | 'cosmic';
  children: React.ReactNode;
  className?: string;
  decorativeElements?: boolean;
}

const GradientBackground: React.FC<GradientBackgroundProps> = ({
  variant = 'primary',
  children,
  className,
  decorativeElements = true
}) => {
  const gradientClasses = {
    primary: 'bg-instagram-primary',
    secondary: 'bg-instagram-secondary',
    story: 'bg-instagram-story',
    subtle: 'bg-instagram-subtle',
    warm: 'bg-instagram-warm',
    sunset: 'bg-instagram-sunset',
    ocean: 'bg-instagram-ocean',
    forest: 'bg-instagram-forest',
    cosmic: 'bg-instagram-cosmic'
  };

  return (
    <div className={cn(
      'min-h-screen relative overflow-hidden flex flex-col',
      gradientClasses[variant],
      className
    )}>
      {decorativeElements && (
        <>
          {/* Background decorative elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/10"></div>
          <div className="absolute top-20 left-10 w-72 h-72 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-white/3 rounded-full blur-3xl"></div>
          {/* Additional floating elements */}
          <div className="absolute top-1/4 right-1/4 w-32 h-32 bg-white/10 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute bottom-1/4 left-1/4 w-48 h-48 bg-white/8 rounded-full blur-2xl animate-pulse"></div>
        </>
      )}
      {children}
    </div>
  );
};

export { GradientBackground };
