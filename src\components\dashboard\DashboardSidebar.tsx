'use client';

import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  User,
  Settings,
  Building2,
  Users,
  FileText,
  MessageCircle,
  Euro,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Send,
  Inbox,
} from 'lucide-react';
import { useState } from 'react';

interface SidebarItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

interface DashboardSidebarProps {
  userType: 'influencer' | 'business';
  className?: string;
}

export function DashboardSidebar({
  userType,
  className,
}: DashboardSidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { signOut } = useAuth();
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Navigacija za influencer korisnike
  const influencerNavigation: SidebarItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/influencer',
      icon: LayoutDashboard,
      description: 'Pregled aktivnosti i statistika',
    },
    {
      name: 'Moj račun',
      href: '/dashboard/influencer/account',
      icon: User,
      description: 'Lični podaci i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/influencer/profile',
      icon: Settings,
      description: 'Javni profil i cijene',
    },
    {
      name: 'Kampanje',
      href: '/marketplace/campaigns',
      icon: FileText,
      description: 'Dostupne kampanje',
    },
    {
      name: 'Direktne ponude',
      href: '/dashboard/influencer/offers',
      icon: Inbox,
      description: 'Direktne ponude od biznisa',
    },
    {
      name: 'Moje aplikacije',
      href: '/dashboard/influencer/applications',
      icon: FileText,
      description: 'Aplikacije na kampanje',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: MessageCircle,
      description: 'Komunikacija sa brendovima',
    },
    {
      name: 'Zarade',
      href: '/dashboard/influencer/earnings',
      icon: Euro,
      description: 'Historija zarada',
    },
  ];

  // Navigacija za biznis korisnike
  const businessNavigation: SidebarItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/biznis',
      icon: LayoutDashboard,
      description: 'Pregled kampanja i statistika',
    },
    {
      name: 'Moj račun',
      href: '/dashboard/biznis/account',
      icon: Building2,
      description: 'Podaci o firmi i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/biznis/profile',
      icon: Settings,
      description: 'Javni profil firme',
    },
    {
      name: 'Moje kampanje',
      href: '/dashboard/campaigns',
      icon: FileText,
      description: 'Upravljanje kampanjama',
    },
    {
      name: 'Aplikacije',
      href: '/dashboard/biznis/applications',
      icon: FileText,
      description: 'Aplikacije na kampanje',
    },
    {
      name: 'Moje ponude',
      href: '/dashboard/biznis/offers',
      icon: Send,
      description: 'Direktne ponude influencerima',
    },
    {
      name: 'Influenceri',
      href: '/marketplace/influencers',
      icon: Users,
      description: 'Pronađi influencere',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: MessageCircle,
      description: 'Komunikacija sa influencerima',
    },
  ];

  const navigation =
    userType === 'influencer' ? influencerNavigation : businessNavigation;

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div
      className={cn(
        'flex flex-col h-full bg-card border-r border-border transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">
                🔗
              </span>
            </div>
            <span className="text-lg font-bold text-foreground">
              InfluConnect
            </span>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 p-0"
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* User Type Badge */}
      {!isCollapsed && (
        <div className="px-4 py-2">
          <div
            className={cn(
              'inline-flex items-center px-2 py-1 rounded-md text-xs font-medium',
              userType === 'influencer'
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            )}
          >
            {userType === 'influencer' ? (
              <>
                <User className="w-3 h-3 mr-1" />
                Influencer
              </>
            ) : (
              <>
                <Building2 className="w-3 h-3 mr-1" />
                Biznis
              </>
            )}
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1">
        {navigation.map(item => {
          const isActive =
            pathname === item.href || pathname.startsWith(item.href + '/');

          return (
            <Link key={item.name} href={item.href}>
              <div
                className={cn(
                  'group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                )}
              >
                <item.icon
                  className={cn(
                    'flex-shrink-0 h-5 w-5',
                    isCollapsed ? 'mx-auto' : 'mr-3'
                  )}
                />
                {!isCollapsed && (
                  <div className="flex-1">
                    <div className="font-medium">{item.name}</div>
                    {item.description && (
                      <div className="text-xs opacity-75 mt-0.5">
                        {item.description}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-2 border-t border-border">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSignOut}
          className={cn(
            'w-full justify-start text-muted-foreground hover:text-foreground',
            isCollapsed && 'justify-center'
          )}
        >
          <LogOut className={cn('h-4 w-4', !isCollapsed && 'mr-2')} />
          {!isCollapsed && 'Odjavi se'}
        </Button>
      </div>
    </div>
  );
}
