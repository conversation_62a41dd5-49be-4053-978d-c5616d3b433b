'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Euro, Send, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { createDirectOffer } from '@/lib/offers';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

// Schema za validaciju
const offerSchema = z.object({
  title: z
    .string()
    .min(5, 'Naslov mora imati najmanje 5 karaktera')
    .max(200, 'Naslov je predugačak'),
  description: z.string().min(20, 'Opis mora imati najmanje 20 karaktera'),
  budget: z
    .number()
    .min(25, 'Minimalni budžet je 25 €')
    .max(25000, 'Maksimalni budžet je 25,000 €'),
  contentTypes: z
    .array(z.string())
    .min(1, 'Morate odabrati najmanje jedan tip sadržaja'),
  platforms: z
    .array(z.string())
    .min(1, 'Morate odabrati najmanje jednu platformu'),
  deadline: z.date().optional(),
  requirements: z.string().optional(),
  deliverables: z.string().optional(),
});

type OfferForm = z.infer<typeof offerSchema>;

// Tipovi za platforme
interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface DirectOfferFormProps {
  influencerId: string;
  influencerName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

// Konfiguracija tipova sadržaja po platformama
const platformContentTypes = [
  {
    platform: 'Instagram',
    types: ['Instagram Post', 'Instagram Story', 'Instagram Reel']
  },
  {
    platform: 'TikTok',
    types: ['TikTok Video']
  },
  {
    platform: 'YouTube',
    types: ['YouTube Video', 'YouTube Short']
  }
];

export function DirectOfferForm({
  influencerId,
  influencerName,
  onSuccess,
  onCancel,
}: DirectOfferFormProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [platforms, setPlatforms] = useState<Platform[]>([]);

  // State za platforme i tipove sadržaja
  const [selectedPlatformData, setSelectedPlatformData] = useState<{
    [platformId: number]: {
      selected: boolean;
      contentTypes: string[];
    }
  }>({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    setError,
  } = useForm<OfferForm>({
    resolver: zodResolver(offerSchema),
    defaultValues: {
      contentTypes: [],
      platforms: [],
    },
  });

  // Load platforms on mount
  useEffect(() => {
    const loadPlatforms = async () => {
      try {
        const { data, error } = await supabase
          .from('platforms')
          .select('*')
          .eq('is_active', true)
          .not('name', 'in', '(Facebook,Twitter/X)')
          .order('name');

        if (data) {
          setPlatforms(data);
          // Initialize platform data state
          const initialPlatformData: any = {};
          data.forEach(platform => {
            initialPlatformData[platform.id] = {
              selected: false,
              contentTypes: []
            };
          });
          setSelectedPlatformData(initialPlatformData);
        }
      } catch (error) {
        console.error('Error loading platforms:', error);
      }
    };

    loadPlatforms();
  }, []);

  // Helper funkcije za rad sa platformama
  const togglePlatform = (platformId: number) => {
    setSelectedPlatformData(prev => ({
      ...prev,
      [platformId]: {
        ...prev[platformId],
        selected: !prev[platformId]?.selected,
        contentTypes: prev[platformId]?.selected ? [] : prev[platformId]?.contentTypes || []
      }
    }));
  };

  const toggleContentType = (platformId: number, contentType: string) => {
    setSelectedPlatformData(prev => {
      const currentTypes = prev[platformId]?.contentTypes || [];
      const newTypes = currentTypes.includes(contentType)
        ? currentTypes.filter(type => type !== contentType)
        : [...currentTypes, contentType];

      return {
        ...prev,
        [platformId]: {
          ...prev[platformId],
          contentTypes: newTypes
        }
      };
    });
  };

  const getAvailableContentTypes = (platformName: string): string[] => {
    const platformConfig = platformContentTypes.find(p => p.platform === platformName);
    return platformConfig?.types || [];
  };

  // Update React Hook Form when platform data changes
  useEffect(() => {
    const selectedPlatforms = Object.entries(selectedPlatformData).filter(
      ([_, data]) => data.selected && data.contentTypes.length > 0
    );

    const allContentTypes = selectedPlatforms
      .flatMap(([_, data]) => data.contentTypes)
      .filter((type, index, array) => array.indexOf(type) === index);

    const allPlatforms = selectedPlatforms.map(([platformId, _]) => {
      const platform = platforms.find(p => p.id === parseInt(platformId));
      return platform?.name || '';
    }).filter(Boolean);

    setValue('contentTypes', allContentTypes);
    setValue('platforms', allPlatforms);
  }, [selectedPlatformData, platforms, setValue]);

  const onSubmit = async (data: OfferForm) => {
    if (!user) return;

    // Validate platform selection
    const selectedPlatforms = Object.entries(selectedPlatformData).filter(
      ([_, data]) => data.selected && data.contentTypes.length > 0
    );

    if (selectedPlatforms.length === 0) {
      setError('root', { message: 'Morate odabrati najmanje jednu platformu sa tipom sadržaja' });
      return;
    }

    setIsLoading(true);

    try {
      // Prepare platform and content type data
      const allContentTypes = selectedPlatforms
        .flatMap(([_, data]) => data.contentTypes)
        .filter((type, index, array) => array.indexOf(type) === index); // Remove duplicates

      const allPlatforms = selectedPlatforms.map(([platformId, _]) => {
        const platform = platforms.find(p => p.id === parseInt(platformId));
        return platform?.name || '';
      }).filter(Boolean);

      const offerData = {
        influencer_id: influencerId,
        title: data.title,
        description: data.description,
        budget: data.budget,
        content_types: allContentTypes,
        platforms: allPlatforms,
        deadline: data.deadline ? format(data.deadline, 'yyyy-MM-dd') : null,
        requirements: data.requirements || null,
      };

      const { error } = await createDirectOffer(offerData);

      if (error) {
        setError('root', { message: 'Greška pri slanju ponude' });
        return;
      }

      toast.success('Ponuda je uspješno poslana!');
      onSuccess();
    } catch (error) {
      setError('root', { message: 'Neočekivana greška' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold">Pošaljite direktnu ponudu</h2>
        <p className="text-muted-foreground mt-1">
          Pošaljite ponudu za saradnju sa{' '}
          <span className="font-medium">{influencerName}</span>
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Kartica 1 */}
        <Card>
          <CardContent className="space-y-4 pt-6">
            {/* Naslov */}
            <div>
              <Label htmlFor="title">Naslov ponude *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Promocija novog proizvoda"
              />
              {errors.title && (
                <p className="text-sm text-destructive mt-1">
                  {errors.title.message}
                </p>
              )}
            </div>

            {/* Opis */}
            <div>
              <Label htmlFor="description">Kako želite da predstavim Vaš proizvod ili uslugu *</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Želimo da snimite kratki video u kojem koristite našu kremu, podijelite iskreno iskustvo u Instagram storyju i objavite jednu profesionalnu fotografiju proizvoda uz opis."
                rows={4}
              />
              {errors.description && (
                <p className="text-sm text-destructive mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Koju tip sadržaja želite da kreiram i objavim? */}
            <div>
              <Label>Koju tip sadržaja želite da kreiram i objavim? *</Label>
              <div className="space-y-4 mt-3">
                {platforms.map(platform => {
                  const availableTypes = getAvailableContentTypes(platform.name);
                  const isSelected = selectedPlatformData[platform.id]?.selected || false;
                  const selectedTypes = selectedPlatformData[platform.id]?.contentTypes || [];

                  return (
                    <div key={platform.id} className="border rounded-lg p-4">
                      <div className="flex items-center space-x-3 mb-3">
                        <Checkbox
                          id={`platform-${platform.id}`}
                          checked={isSelected}
                          onCheckedChange={() => togglePlatform(platform.id)}
                        />
                        <Label
                          htmlFor={`platform-${platform.id}`}
                          className="flex items-center gap-2 text-base font-medium cursor-pointer"
                        >
                          <span className="text-lg">{platform.icon}</span>
                          {platform.name}
                        </Label>
                      </div>

                      {isSelected && (
                        <div className="ml-6 space-y-2">
                          <p className="text-sm text-muted-foreground">Tipovi sadržaja:</p>
                          <div className="grid grid-cols-2 gap-2">
                            {availableTypes.map(type => (
                              <div key={type} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`${platform.id}-${type}`}
                                  checked={selectedTypes.includes(type)}
                                  onCheckedChange={() => toggleContentType(platform.id, type)}
                                />
                                <Label
                                  htmlFor={`${platform.id}-${type}`}
                                  className="text-sm cursor-pointer"
                                >
                                  {type}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Cijena */}
            <div>
              <Label htmlFor="budget">Cijena *</Label>
              <p className="text-sm text-muted-foreground mb-2">
                Koliko ste spremni platiti za ovu uslugu?
              </p>
              <div className="relative">
                <Euro className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="budget"
                  type="number"
                  {...register('budget', { valueAsNumber: true })}
                  placeholder=""
                  className="pl-10"
                />
              </div>
              {errors.budget && (
                <p className="text-sm text-destructive mt-1">
                  {errors.budget.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Kartica 2 */}
        <Card>
          <CardContent className="space-y-4 pt-6">
            {/* Specifični zahtjevi */}
            <div>
              <Label htmlFor="requirements">Specifični zahtjevi</Label>
              <Textarea
                id="requirements"
                {...register('requirements')}
                placeholder="Specifični zahtjevi za kreiranje sadržaja, zabranjene stvari, posebne napomene..."
                rows={3}
              />
            </div>

            {/* Deadline */}
            <div>
              <Label>Koji je krajnji rok za kreiranje objava?</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal mt-2',
                      !watch('deadline') && 'text-muted-foreground'
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {watch('deadline')
                      ? format(watch('deadline'), 'dd.MM.yyyy')
                      : 'Odaberite datum'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={watch('deadline')}
                    onSelect={date => setValue('deadline', date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </CardContent>
        </Card>

        {/* Error message */}
        {errors.root && (
          <div className="text-sm text-destructive text-center">
            {errors.root.message}
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel}>
            Otkaži
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Šalje se...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Pošalji ponudu
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
