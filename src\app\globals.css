@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.147 0.004 49.25);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.147 0.004 49.25);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.147 0.004 49.25);
  --primary: oklch(0.216 0.006 56.043);
  --primary-foreground: oklch(0.985 0.001 106.423);
  --secondary: oklch(0.97 0.001 106.424);
  --secondary-foreground: oklch(0.216 0.006 56.043);
  --muted: oklch(0.97 0.001 106.424);
  --muted-foreground: oklch(0.553 0.013 58.071);
  --accent: oklch(0.97 0.001 106.424);
  --accent-foreground: oklch(0.216 0.006 56.043);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.923 0.003 48.717);
  --input: oklch(0.923 0.003 48.717);
  --ring: oklch(0.709 0.01 56.259);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.001 106.423);
  --sidebar-foreground: oklch(0.147 0.004 49.25);
  --sidebar-primary: oklch(0.216 0.006 56.043);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.97 0.001 106.424);
  --sidebar-accent-foreground: oklch(0.216 0.006 56.043);
  --sidebar-border: oklch(0.923 0.003 48.717);
  --sidebar-ring: oklch(0.709 0.01 56.259);
}

.dark {
  --background: oklch(0.147 0.004 49.25);
  --foreground: oklch(0.985 0.001 106.423);
  --card: oklch(0.216 0.006 56.043);
  --card-foreground: oklch(0.985 0.001 106.423);
  --popover: oklch(0.216 0.006 56.043);
  --popover-foreground: oklch(0.985 0.001 106.423);
  --primary: oklch(0.923 0.003 48.717);
  --primary-foreground: oklch(0.216 0.006 56.043);
  --secondary: oklch(0.268 0.007 34.298);
  --secondary-foreground: oklch(0.985 0.001 106.423);
  --muted: oklch(0.268 0.007 34.298);
  --muted-foreground: oklch(0.709 0.01 56.259);
  --accent: oklch(0.268 0.007 34.298);
  --accent-foreground: oklch(0.985 0.001 106.423);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.553 0.013 58.071);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.216 0.006 56.043);
  --sidebar-foreground: oklch(0.985 0.001 106.423);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.268 0.007 34.298);
  --sidebar-accent-foreground: oklch(0.985 0.001 106.423);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.553 0.013 58.071);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      'Inter',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      sans-serif;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

/* Custom Design System Variables */
:root {
  /* Additional spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Animations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Accordion animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

/* Instagram-inspired CSS variables */
:root {
  /* Instagram-inspired gradients */
  --instagram-gradient-primary: linear-gradient(135deg, #833ab4 0%, #fd1d1d 50%, #fcb045 100%);
  --instagram-gradient-secondary: linear-gradient(135deg, #405de6 0%, #5851db 25%, #833ab4 50%, #c13584 75%, #e1306c 100%);
  --instagram-gradient-story: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  --instagram-gradient-subtle: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --instagram-gradient-warm: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);

  /* Additional trendy gradients */
  --instagram-gradient-sunset: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #ff9ff3 100%);
  --instagram-gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --instagram-gradient-forest: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  --instagram-gradient-cosmic: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);

  /* Instagram brand colors */
  --instagram-purple: #833ab4;
  --instagram-pink: #e1306c;
  --instagram-orange: #fcb045;
  --instagram-red: #fd1d1d;
  --instagram-blue: #405de6;

  /* Gradient overlays for backgrounds */
  --gradient-overlay-light: linear-gradient(135deg, rgba(131, 58, 180, 0.1) 0%, rgba(253, 29, 29, 0.1) 50%, rgba(252, 176, 69, 0.1) 100%);
  --gradient-overlay-medium: linear-gradient(135deg, rgba(131, 58, 180, 0.3) 0%, rgba(253, 29, 29, 0.3) 50%, rgba(252, 176, 69, 0.3) 100%);
  --gradient-overlay-dark: linear-gradient(135deg, rgba(131, 58, 180, 0.7) 0%, rgba(253, 29, 29, 0.7) 50%, rgba(252, 176, 69, 0.7) 100%);

  /* Glass morphism variations */
  --glass-light: rgba(255, 255, 255, 0.1);
  --glass-medium: rgba(255, 255, 255, 0.15);
  --glass-strong: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(131, 58, 180, 0.15);
  --glass-shadow-strong: 0 12px 40px rgba(131, 58, 180, 0.25);
}

/* Instagram-inspired utility classes */
@layer utilities {
  /* Background gradients */
  .bg-instagram-primary {
    background: var(--instagram-gradient-primary);
  }

  .bg-instagram-secondary {
    background: var(--instagram-gradient-secondary);
  }

  .bg-instagram-story {
    background: var(--instagram-gradient-story);
  }

  .bg-instagram-subtle {
    background: var(--instagram-gradient-subtle);
  }

  .bg-instagram-warm {
    background: var(--instagram-gradient-warm);
  }

  .bg-instagram-sunset {
    background: var(--instagram-gradient-sunset);
  }

  .bg-instagram-ocean {
    background: var(--instagram-gradient-ocean);
  }

  .bg-instagram-forest {
    background: var(--instagram-gradient-forest);
  }

  .bg-instagram-cosmic {
    background: var(--instagram-gradient-cosmic);
  }

  /* Text gradients */
  .text-instagram-primary {
    background: var(--instagram-gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-instagram-secondary {
    background: var(--instagram-gradient-secondary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Border gradients */
  .border-instagram-primary {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box, var(--instagram-gradient-primary) border-box;
  }

  .border-instagram-secondary {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box, var(--instagram-gradient-secondary) border-box;
  }

  /* Gradient overlays */
  .overlay-instagram-light::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-overlay-light);
    pointer-events: none;
  }

  .overlay-instagram-medium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-overlay-medium);
    pointer-events: none;
  }

  /* Animated gradients */
  .bg-instagram-animated {
    background: var(--instagram-gradient-primary);
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Glass morphism effect with Instagram colors */
  .glass-instagram {
    background: var(--glass-light);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-instagram-medium {
    background: var(--glass-medium);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-instagram-strong {
    background: var(--glass-strong);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow-strong);
  }

  /* Enhanced card styling */
  .card-instagram {
    @apply rounded-2xl shadow-2xl border-0;
    background: var(--glass-light);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .card-instagram-solid {
    @apply rounded-2xl shadow-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 40px rgba(131, 58, 180, 0.1);
  }

  /* Button enhancements */
  .btn-instagram-primary {
    @apply rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5;
    background: var(--instagram-gradient-primary);
    color: white;
  }

  .btn-instagram-glass {
    @apply rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5;
    background: var(--glass-medium);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    color: white;
  }

  .btn-instagram-white {
    @apply rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-0.5;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    color: var(--instagram-purple);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* Input enhancements */
  .input-instagram {
    @apply rounded-xl border-0 shadow-sm transition-all duration-300;
    background: var(--glass-light);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    color: white;
  }

  .input-instagram::placeholder {
    color: rgba(255, 255, 255, 0.6);
  }

  .input-instagram:focus {
    background: var(--glass-medium);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
  }
}

/* Touch-friendly minimum sizes for mobile */
@layer components {
  .btn,
  button:not(.btn-sm),
  input[type='button'],
  input[type='submit'] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Hover effects */
  .hover-lift {
    transition: transform var(--duration-normal) var(--ease-in-out);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
  }

  /* Mobile-first form inputs */
  input[type='text'],
  input[type='email'],
  input[type='password'],
  input[type='number'],
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Hide scrollbar for tabs */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
